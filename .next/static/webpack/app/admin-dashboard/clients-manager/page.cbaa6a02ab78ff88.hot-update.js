"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/admin/clients/invoice-form-modal.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvoiceModal: () => (/* binding */ InvoiceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _shared_modal_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/modal-system */ \"(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\");\n/* __next_internal_client_entry_do_not_use__ InvoiceModal auto */ \nvar _s = $RefreshSig$();\n\n// Imports\n\n\n\n\n\n\n// ========================================\n// MAIN COMPONENT\n// ========================================\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSubmit, title, initialData, client, project } = param;\n    _s();\n    // ========================================\n    // STATE MANAGEMENT\n    // ========================================\n    // Loading states for different operations\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [itemsLoading, setItemsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [autoHeight, setAutoHeight] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Form data state - contains all invoice fields\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        invoiceNumber: '',\n        dueDate: '',\n        status: 'DRAFT',\n        description: '',\n        taxRate: 0,\n        subtotal: 0,\n        taxAmount: 0,\n        totalAmount: 0,\n        paidAt: ''\n    });\n    // Invoice items state - array of line items\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: 'temp-default',\n            description: '',\n            quantity: 1,\n            unitPrice: 0,\n            totalPrice: 0\n        }\n    ]);\n    // ========================================\n    // EFFECTS\n    // ========================================\n    // Calculate totals when items or tax rate changes - memoized for performance\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const subtotal = items.reduce({\n                \"InvoiceModal.useEffect.subtotal\": (sum, item)=>sum + item.totalPrice\n            }[\"InvoiceModal.useEffect.subtotal\"], 0);\n            const taxAmount = subtotal * formData.taxRate / 100;\n            const totalAmount = subtotal + taxAmount;\n            setFormData({\n                \"InvoiceModal.useEffect\": (prev)=>({\n                        ...prev,\n                        subtotal,\n                        taxAmount,\n                        totalAmount\n                    })\n            }[\"InvoiceModal.useEffect\"]);\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        formData.taxRate\n    ]);\n    // Auto-height logic for items container - always show all items\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            // Always keep auto-height enabled to show all items\n            setAutoHeight(true);\n            // Trigger auto-resize when items change\n            if (isOpen) {\n                const triggerResize = {\n                    \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Force a reflow and resize\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.triggerResize\"], 10);\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.triggerResize\"];\n                // Trigger resize after items change\n                setTimeout(triggerResize, 50);\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        isOpen\n    ]);\n    // Auto-resize modal based on content - fully automatic\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const resizeModalToContent = {\n                    \"InvoiceModal.useEffect.resizeModalToContent\": ()=>{\n                        // Find the modal container\n                        const modalContainer = document.querySelector('.modal-container');\n                        const modalContent = document.querySelector('.modal-content-body');\n                        if (modalContainer && modalContent) {\n                            // Remove all height restrictions\n                            modalContainer.style.height = 'auto';\n                            modalContainer.style.maxHeight = 'none';\n                            modalContainer.style.minHeight = 'auto';\n                            modalContent.style.overflow = 'visible';\n                            modalContent.style.maxHeight = 'none';\n                            modalContent.style.height = 'auto';\n                            // Force a reflow to get accurate measurements\n                            modalContainer.offsetHeight;\n                            // Get the natural content height\n                            const contentHeight = modalContainer.scrollHeight;\n                            // Apply bounds but allow natural sizing\n                            const minHeight = 400;\n                            const maxHeight = window.innerHeight - 20;\n                            const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));\n                            // Apply the calculated height\n                            modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                            modalContainer.style.maxHeight = \"\".concat(maxHeight, \"px\");\n                            modalContent.style.overflow = 'visible';\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.resizeModalToContent\"];\n                // Immediate resize\n                resizeModalToContent();\n                // Resize after content is rendered\n                const timer = setTimeout(resizeModalToContent, 100);\n                // Resize when items change\n                const resizeTimer = setTimeout(resizeModalToContent, 50);\n                // Use ResizeObserver for real-time content changes\n                let resizeObserver = null;\n                const modalContent = document.querySelector('.modal-content-body');\n                if (modalContent && window.ResizeObserver) {\n                    resizeObserver = new ResizeObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    resizeObserver.observe(modalContent);\n                }\n                // Also observe the items container for changes\n                const itemsContainer = document.querySelector('.bg-white.border.border-gray-200.rounded-lg');\n                if (itemsContainer && window.ResizeObserver) {\n                    resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.observe(itemsContainer);\n                }\n                // Use MutationObserver to detect any DOM changes\n                let mutationObserver = null;\n                if (window.MutationObserver) {\n                    mutationObserver = new MutationObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    const modalContent = document.querySelector('.modal-content-body');\n                    if (modalContent) {\n                        mutationObserver.observe(modalContent, {\n                            childList: true,\n                            subtree: true,\n                            attributes: true,\n                            attributeFilter: [\n                                'style',\n                                'class'\n                            ]\n                        });\n                    }\n                }\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>{\n                        clearTimeout(timer);\n                        clearTimeout(resizeTimer);\n                        if (resizeObserver) {\n                            resizeObserver.disconnect();\n                        }\n                        if (mutationObserver) {\n                            mutationObserver.disconnect();\n                        }\n                    }\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen,\n        items\n    ]);\n    // Handle window resize to maintain proper modal sizing\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const handleWindowResize = {\n                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                    if (isOpen) {\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Auto-resize on window resize\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.handleWindowResize\"], 10);\n                        }\n                    }\n                }\n            }[\"InvoiceModal.useEffect.handleWindowResize\"];\n            window.addEventListener('resize', handleWindowResize);\n            return ({\n                \"InvoiceModal.useEffect\": ()=>window.removeEventListener('resize', handleWindowResize)\n            })[\"InvoiceModal.useEffect\"];\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Continuous auto-resize check for maximum automation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const continuousResize = {\n                    \"InvoiceModal.useEffect.continuousResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            const contentHeight = modalContainer.scrollHeight;\n                            const maxHeight = window.innerHeight - 20;\n                            const currentHeight = parseInt(modalContainer.style.height) || 0;\n                            const idealHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                            // Only resize if there's a significant difference\n                            if (Math.abs(currentHeight - idealHeight) > 10) {\n                                modalContainer.style.height = \"\".concat(idealHeight, \"px\");\n                            }\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.continuousResize\"];\n                // Check every 500ms for any changes\n                const interval = setInterval(continuousResize, 500);\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>clearInterval(interval)\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const loadInvoiceData = {\n                \"InvoiceModal.useEffect.loadInvoiceData\": async ()=>{\n                    if (initialData) {\n                        setFormData({\n                            invoiceNumber: initialData.invoiceNumber || '',\n                            dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',\n                            status: initialData.status || 'DRAFT',\n                            description: initialData.description || '',\n                            taxRate: Number(initialData.taxRate) || 0,\n                            subtotal: Number(initialData.subtotal) || 0,\n                            taxAmount: Number(initialData.taxAmount) || 0,\n                            totalAmount: Number(initialData.totalAmount) || 0,\n                            paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''\n                        });\n                        // Load existing items from API\n                        try {\n                            setItemsLoading(true);\n                            const invoiceId = String(initialData.id);\n                            console.log('Fetching items for invoice ID:', invoiceId);\n                            const response = await fetch(\"/api/admin/invoices/\".concat(invoiceId, \"/items\"));\n                            console.log('Items fetch response status:', response.status, response.statusText);\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('Items fetch result:', result);\n                                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                                    // Parse Decimal.js objects with format {s, e, d}\n                                    const parseDecimal = {\n                                        \"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\": (decimalObj)=>{\n                                            if (!decimalObj || typeof decimalObj !== 'object') return 0;\n                                            const { s, e, d } = decimalObj;\n                                            if (s === undefined || e === undefined || !Array.isArray(d)) return 0;\n                                            // Convert digits array to number\n                                            const digits = d.join('');\n                                            if (!digits) return 0;\n                                            // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)\n                                            const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1);\n                                            return isNaN(value) ? 0 : value;\n                                        }\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\"];\n                                    // Map database items to form items\n                                    const mappedItems = result.data.map({\n                                        \"InvoiceModal.useEffect.loadInvoiceData.mappedItems\": (item)=>({\n                                                id: String(item.id),\n                                                description: String(item.description || ''),\n                                                quantity: parseDecimal(item.quantity),\n                                                unitPrice: parseDecimal(item.unitprice),\n                                                totalPrice: parseDecimal(item.totalprice)\n                                            })\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.mappedItems\"]);\n                                    console.log('Mapped items:', mappedItems);\n                                    setItems(mappedItems);\n                                } else {\n                                    // No items found, use default empty item\n                                    console.log('No items found in response, using default empty item');\n                                    setItems([\n                                        {\n                                            id: 'temp-default',\n                                            description: '',\n                                            quantity: 1,\n                                            unitPrice: 0,\n                                            totalPrice: 0\n                                        }\n                                    ]);\n                                }\n                            } else {\n                                // API error, use default empty item\n                                console.error('Failed to fetch invoice items:', response.status, response.statusText);\n                                setItems([\n                                    {\n                                        id: 'temp-default',\n                                        description: '',\n                                        quantity: 1,\n                                        unitPrice: 0,\n                                        totalPrice: 0\n                                    }\n                                ]);\n                            }\n                        } catch (error) {\n                            // Network or other error, use default empty item\n                            console.error('Error loading invoice items:', error);\n                            setItems([\n                                {\n                                    id: 'temp-default',\n                                    description: '',\n                                    quantity: 1,\n                                    unitPrice: 0,\n                                    totalPrice: 0\n                                }\n                            ]);\n                        } finally{\n                            setItemsLoading(false);\n                        }\n                    } else {\n                        // Reset form for new invoice\n                        setFormData({\n                            invoiceNumber: '',\n                            dueDate: '',\n                            status: 'DRAFT',\n                            description: '',\n                            taxRate: 0,\n                            subtotal: 0,\n                            taxAmount: 0,\n                            totalAmount: 0,\n                            paidAt: ''\n                        });\n                        setItems([\n                            {\n                                id: 'temp-default',\n                                description: '',\n                                quantity: 1,\n                                unitPrice: 0,\n                                totalPrice: 0\n                            }\n                        ]);\n                    }\n                }\n            }[\"InvoiceModal.useEffect.loadInvoiceData\"];\n            if (isOpen) {\n                loadInvoiceData();\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        initialData,\n        isOpen\n    ]);\n    const addItem = ()=>{\n        setItems([\n            ...items,\n            {\n                id: \"temp-\".concat(Date.now()),\n                description: '',\n                quantity: 1,\n                unitPrice: 0,\n                totalPrice: 0\n            }\n        ]);\n    };\n    const removeItem = (index)=>{\n        if (items.length > 1) {\n            setItems(items.filter((_, i)=>i !== index));\n        }\n    };\n    const updateItem = (index, field, value)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems[index] = {\n            ...updatedItems[index],\n            [field]: value\n        };\n        // Auto-calculate total price when quantity or unit price changes\n        if (field === 'quantity' || field === 'unitPrice') {\n            const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity;\n            const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice;\n            updatedItems[index].totalPrice = quantity * unitPrice;\n        }\n        setItems(updatedItems);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            // Validate items\n            const validItems = items.filter((item)=>item.description.trim() !== '');\n            if (validItems.length === 0) {\n                alert('Please add at least one item to the invoice.');\n                setLoading(false);\n                return;\n            }\n            // Validate that all items have positive whole number quantities and prices\n            const invalidItems = validItems.filter((item)=>{\n                // Handle floating-point precision issues by checking if quantity is close to an integer\n                const roundedQuantity = Math.round(item.quantity);\n                const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001;\n                const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger);\n                const isPriceValid = item.unitPrice >= 0;\n                if (!isQuantityValid || !isPriceValid) {\n                    console.log('Invalid item:', {\n                        description: item.description,\n                        originalQuantity: item.quantity,\n                        roundedQuantity: roundedQuantity,\n                        quantityType: typeof item.quantity,\n                        isInteger: Number.isInteger(item.quantity),\n                        isCloseToInteger: isCloseToInteger,\n                        unitPrice: item.unitPrice,\n                        isQuantityValid,\n                        isPriceValid\n                    });\n                }\n                return !isQuantityValid || !isPriceValid;\n            });\n            if (invalidItems.length > 0) {\n                console.log('Invalid items found:', invalidItems);\n                alert('All items must have positive whole number quantities and non-negative unit prices.');\n                setLoading(false);\n                return;\n            }\n            const submitData = {\n                ...formData,\n                items: validItems.map((item)=>{\n                    var _item_id;\n                    return {\n                        ...item,\n                        // Round quantity to handle floating-point precision issues when submitting\n                        quantity: Math.round(item.quantity),\n                        // Remove temporary IDs for new items\n                        id: ((_item_id = item.id) === null || _item_id === void 0 ? void 0 : _item_id.startsWith('temp-')) ? undefined : item.id\n                    };\n                }),\n                clientId: client.id,\n                projectId: project.id\n            };\n            console.log('Submitting invoice data:', submitData);\n            console.log('Valid items:', validItems);\n            await onSubmit(submitData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting invoice:', error);\n            alert('Failed to save invoice. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDownloadPDF = async ()=>{\n        try {\n            console.log('Starting PDF download with data:', {\n                formData,\n                client,\n                project\n            });\n            // Try basic HTML-to-PDF approach first\n            let response = await fetch('/api/invoices/generate-pdf-basic', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Basic PDF API response status:', response.status);\n            if (response.ok) {\n                // Open the HTML in a new window for printing/saving as PDF\n                const htmlContent = await response.text();\n                const printWindow = window.open('', '_blank');\n                if (printWindow) {\n                    printWindow.document.write(htmlContent);\n                    printWindow.document.close();\n                    printWindow.focus();\n                    // The HTML includes auto-print script, so it will automatically open print dialog\n                    console.log('PDF download completed successfully via HTML');\n                    return;\n                }\n            }\n            // If basic approach fails, try simple PDF generation\n            console.log('Basic PDF failed, trying simple PDF approach...');\n            response = await fetch('/api/invoices/generate-pdf-simple', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Simple PDF API response status:', response.status);\n            // If simple PDF fails, try the original Puppeteer approach\n            if (!response.ok) {\n                console.log('Simple PDF failed, trying Puppeteer approach...');\n                response = await fetch('/api/invoices/generate-pdf', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter((item)=>item.description.trim() !== '')\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Puppeteer PDF API response status:', response.status);\n            }\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                console.error('PDF generation failed:', errorData);\n                throw new Error(\"Failed to generate PDF: \".concat(errorData.details || errorData.error || 'Unknown error'));\n            }\n            const blob = await response.blob();\n            console.log('PDF blob created, size:', blob.size);\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"invoice-\".concat(formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''), \".pdf\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('PDF download completed successfully');\n        } catch (error) {\n            console.error('Download error:', error);\n            alert(\"PDF generation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"5bfaf54eaea5fa1d\",\n                children: \".modal-container.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important;min-height:auto!important}.modal-content-body.jsx-5bfaf54eaea5fa1d{overflow:visible!important;max-height:none!important;height:auto!important}.modal-content.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                title: title,\n                subtitle: \"\".concat(client.companyName, \" - \").concat(project.name),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 15\n                }, void 0),\n                iconColor: \"blue\",\n                disableOverlayClick: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        onClick: (e)=>e.stopPropagation(),\n                        style: {\n                            paddingBottom: '80px',\n                            height: 'auto',\n                            maxHeight: 'none',\n                            overflow: 'visible'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"modal-form-section-icon sample-style\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Invoice Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-4 gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Invoice Number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            invoiceNumber: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Auto-generated\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: [\n                                                                        \"Due Date \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-required\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 652,\n                                                                            columnNumber: 66\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"date\",\n                                                                    required: true,\n                                                                    value: formData.dueDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            dueDate: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.status,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            status: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-select\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DRAFT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Draft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"SENT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Sent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"PAID\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Paid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"OVERDUE\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Overdue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Tax Rate (%)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    max: \"100\",\n                                                                    step: \"0.01\",\n                                                                    value: formData.taxRate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            taxRate: parseFloat(e.target.value) || 0\n                                                                        }),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.description,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    description: e.target.value\n                                                                }),\n                                                            rows: 2,\n                                                            placeholder: \"Invoice description...\",\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-textarea\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"modal-form-section-icon sample-style green\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                \"Invoice Items (\",\n                                                                items.filter((item)=>item.description.trim() !== '').length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addItem,\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    \"Add Item\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        maxHeight: 'none',\n                                                        minHeight: '200px',\n                                                        overflow: 'visible'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 pl-2 border-r border-gray-300\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Qty\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Total\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 text-center\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        itemsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"p-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center space-x-2 text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm\",\n                                                                        children: \"Loading...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 17\n                                                        }, this) : items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors \".concat(index < items.length - 1 ? 'border-b border-gray-100' : ''),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: item.description,\n                                                                            onChange: (e)=>updateItem(index, 'description', e.target.value),\n                                                                            placeholder: \"Item description\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 \".concat(item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"1\",\n                                                                            step: \"1\",\n                                                                            value: item.quantity || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseInt(e.target.value) || 0;\n                                                                                updateItem(index, 'quantity', value);\n                                                                            },\n                                                                            placeholder: \"1\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.quantity <= 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 767,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            value: item.unitPrice || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseFloat(e.target.value) || 0;\n                                                                                updateItem(index, 'unitPrice', value);\n                                                                            },\n                                                                            placeholder: \"0.00\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (item.totalPrice || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>removeItem(index),\n                                                                            disabled: items.length === 1,\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 810,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id || \"item-\".concat(index), true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 13\n                                                }, this),\n                                                items.filter((item)=>item.description.trim() !== '').length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message warning\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon warning\",\n                                                                children: \"⚠️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text warning\",\n                                                                children: \"Please add at least one item to the invoice.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this),\n                                                items.some((item)=>item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message error\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon error\",\n                                                                children: \"❌\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text error\",\n                                                                children: \"All items must have positive whole number quantities and non-negative unit prices.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"modal-form-section-icon sample-style orange\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Invoice Summary\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Subtotal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.subtotal.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Tax (\",\n                                                                    formData.taxRate,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.taxAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Total Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-blue-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.totalAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Payment Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Amount Paid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-green-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 887,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Balance Due:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 892,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-red-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0)).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"mt-2\",\n                                                                children: formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 900,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Paid in Full\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this) : ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-yellow-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Partially Paid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Unpaid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (initialData === null || initialData === void 0 ? void 0 : initialData.payments) && initialData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Recent Payments\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-1\",\n                                                                children: [\n                                                                    initialData.payments.slice(0, 3).map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-gray-600\",\n                                                                                    children: new Date(payment.paymentDate).toLocaleDateString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 924,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"font-medium text-green-600\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        payment.amount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 925,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 923,\n                                                                            columnNumber: 27\n                                                                        }, this)),\n                                                                    initialData.payments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs text-gray-500 text-center pt-1\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            initialData.payments.length - 3,\n                                                                            \" more payments\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            padding: '24px 24px 0 24px',\n                            borderTop: '1px solid #e2e8f0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            position: 'absolute',\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            minHeight: '60px',\n                            opacity: 1,\n                            transform: 'none',\n                            borderBottomLeftRadius: '12px',\n                            borderBottomRightRadius: '12px'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px',\n                                    color: '#64748b',\n                                    fontSize: '14px',\n                                    position: 'absolute',\n                                    left: '24px',\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        style: {\n                                            width: '16px',\n                                            height: '16px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '12px',\n                                    position: 'absolute',\n                                    left: '50%',\n                                    top: '50%',\n                                    transform: 'translate(-50%, -50%)',\n                                    zIndex: 11\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleDownloadPDF,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none',\n                                            marginRight: '12px',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '4px'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                style: {\n                                                    width: '12px',\n                                                    height: '12px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View / Print\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        onClick: handleSubmit,\n                                        disabled: loading,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#3b82f6',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: loading ? 'not-allowed' : 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                                            transform: 'none',\n                                            opacity: loading ? 0.6 : 1\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#2563eb';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#3b82f6';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';\n                                            }\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '12px',\n                                                        height: '12px',\n                                                        border: '2px solid transparent',\n                                                        borderTopColor: 'currentColor',\n                                                        borderRadius: '50%',\n                                                        animation: 'spin 1s linear infinite'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\",\n                                                    children: \"Processing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 17\n                                        }, this) : initialData ? 'Update Invoice' : 'Create Invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 978,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 942,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"oZf/optZkqLL55/AMiQKF2LgdL4=\");\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/main.css":
/*!*****************************!*\
  !*** ./src/styles/main.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5bccd47335d2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvbWFpbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvc3R5bGVzL21haW4uY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNWJjY2Q0NzMzNWQyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/main.css\n"));

/***/ })

});