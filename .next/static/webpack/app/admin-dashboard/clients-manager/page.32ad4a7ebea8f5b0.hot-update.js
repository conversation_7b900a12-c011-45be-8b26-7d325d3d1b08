"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/admin/clients/invoice-form-modal.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvoiceModal: () => (/* binding */ InvoiceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _shared_modal_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/modal-system */ \"(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\");\n/* __next_internal_client_entry_do_not_use__ InvoiceModal auto */ \nvar _s = $RefreshSig$();\n\n// Imports\n\n\n\n\n\n\n// ========================================\n// MAIN COMPONENT\n// ========================================\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSubmit, title, initialData, client, project } = param;\n    _s();\n    // ========================================\n    // STATE MANAGEMENT\n    // ========================================\n    // Loading states for different operations\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [itemsLoading, setItemsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [autoHeight, setAutoHeight] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Form data state - contains all invoice fields\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        invoiceNumber: '',\n        dueDate: '',\n        status: 'DRAFT',\n        description: '',\n        taxRate: 0,\n        subtotal: 0,\n        taxAmount: 0,\n        totalAmount: 0,\n        paidAt: ''\n    });\n    // Invoice items state - array of line items\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: 'temp-default',\n            description: '',\n            quantity: 1,\n            unitPrice: 0,\n            totalPrice: 0\n        }\n    ]);\n    // ========================================\n    // EFFECTS\n    // ========================================\n    // Calculate totals when items or tax rate changes - memoized for performance\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const subtotal = items.reduce({\n                \"InvoiceModal.useEffect.subtotal\": (sum, item)=>sum + item.totalPrice\n            }[\"InvoiceModal.useEffect.subtotal\"], 0);\n            const taxAmount = subtotal * formData.taxRate / 100;\n            const totalAmount = subtotal + taxAmount;\n            setFormData({\n                \"InvoiceModal.useEffect\": (prev)=>({\n                        ...prev,\n                        subtotal,\n                        taxAmount,\n                        totalAmount\n                    })\n            }[\"InvoiceModal.useEffect\"]);\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        formData.taxRate\n    ]);\n    // Auto-height logic for items container - ensures all items are visible\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            // Always keep auto-height enabled to show all items\n            setAutoHeight(true);\n            // Trigger auto-resize when items change\n            if (isOpen) {\n                const triggerResize = {\n                    \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Force a reflow and resize\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.triggerResize\"], 10);\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.triggerResize\"];\n                // Trigger resize after items change\n                setTimeout(triggerResize, 50);\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        isOpen\n    ]);\n    // Auto-resize modal based on content - fully automatic with observers\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const resizeModalToContent = {\n                    \"InvoiceModal.useEffect.resizeModalToContent\": ()=>{\n                        // Find the modal container\n                        const modalContainer = document.querySelector('.modal-container');\n                        const modalContent = document.querySelector('.modal-content-body');\n                        if (modalContainer && modalContent) {\n                            // Remove all height restrictions\n                            modalContainer.style.height = 'auto';\n                            modalContainer.style.maxHeight = 'none';\n                            modalContainer.style.minHeight = 'auto';\n                            modalContent.style.overflow = 'visible';\n                            modalContent.style.maxHeight = 'none';\n                            modalContent.style.height = 'auto';\n                            // Force a reflow to get accurate measurements\n                            modalContainer.offsetHeight;\n                            // Get the natural content height\n                            const contentHeight = modalContainer.scrollHeight;\n                            // Apply bounds but allow natural sizing\n                            const minHeight = 400;\n                            const maxHeight = window.innerHeight - 20;\n                            const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));\n                            // Apply the calculated height\n                            modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                            modalContainer.style.maxHeight = \"\".concat(maxHeight, \"px\");\n                            modalContent.style.overflow = 'visible';\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.resizeModalToContent\"];\n                // Immediate resize\n                resizeModalToContent();\n                // Resize after content is rendered\n                const timer = setTimeout(resizeModalToContent, 100);\n                // Resize when items change\n                const resizeTimer = setTimeout(resizeModalToContent, 50);\n                // Use ResizeObserver for real-time content changes\n                let resizeObserver = null;\n                const modalContent = document.querySelector('.modal-content-body');\n                if (modalContent && window.ResizeObserver) {\n                    resizeObserver = new ResizeObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    resizeObserver.observe(modalContent);\n                }\n                // Also observe the items container for changes\n                const itemsContainer = document.querySelector('.bg-white.border.border-gray-200.rounded-lg');\n                if (itemsContainer && window.ResizeObserver) {\n                    resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.observe(itemsContainer);\n                }\n                // Use MutationObserver to detect any DOM changes\n                let mutationObserver = null;\n                if (window.MutationObserver) {\n                    mutationObserver = new MutationObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    const modalContent = document.querySelector('.modal-content-body');\n                    if (modalContent) {\n                        mutationObserver.observe(modalContent, {\n                            childList: true,\n                            subtree: true,\n                            attributes: true,\n                            attributeFilter: [\n                                'style',\n                                'class'\n                            ]\n                        });\n                    }\n                }\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>{\n                        clearTimeout(timer);\n                        clearTimeout(resizeTimer);\n                        if (resizeObserver) {\n                            resizeObserver.disconnect();\n                        }\n                        if (mutationObserver) {\n                            mutationObserver.disconnect();\n                        }\n                    }\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen,\n        items\n    ]);\n    // Handle window resize to maintain proper modal sizing\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const handleWindowResize = {\n                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                    if (isOpen) {\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Auto-resize on window resize - ensures modal fits new viewport\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.handleWindowResize\"], 10);\n                        }\n                    }\n                }\n            }[\"InvoiceModal.useEffect.handleWindowResize\"];\n            window.addEventListener('resize', handleWindowResize);\n            return ({\n                \"InvoiceModal.useEffect\": ()=>window.removeEventListener('resize', handleWindowResize)\n            })[\"InvoiceModal.useEffect\"];\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Continuous auto-resize check for maximum automation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const continuousResize = {\n                    \"InvoiceModal.useEffect.continuousResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            const contentHeight = modalContainer.scrollHeight;\n                            const maxHeight = window.innerHeight - 20;\n                            const currentHeight = parseInt(modalContainer.style.height) || 0;\n                            const idealHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                            // Only resize if there's a significant difference - prevents unnecessary DOM updates\n                            if (Math.abs(currentHeight - idealHeight) > 10) {\n                                modalContainer.style.height = \"\".concat(idealHeight, \"px\");\n                            }\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.continuousResize\"];\n                // Check every 500ms for any changes - fallback for edge cases\n                const interval = setInterval(continuousResize, 500);\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>clearInterval(interval)\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const loadInvoiceData = {\n                \"InvoiceModal.useEffect.loadInvoiceData\": async ()=>{\n                    if (initialData) {\n                        setFormData({\n                            invoiceNumber: initialData.invoiceNumber || '',\n                            dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',\n                            status: initialData.status || 'DRAFT',\n                            description: initialData.description || '',\n                            taxRate: Number(initialData.taxRate) || 0,\n                            subtotal: Number(initialData.subtotal) || 0,\n                            taxAmount: Number(initialData.taxAmount) || 0,\n                            totalAmount: Number(initialData.totalAmount) || 0,\n                            paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''\n                        });\n                        // Load existing items from API\n                        try {\n                            setItemsLoading(true);\n                            const invoiceId = String(initialData.id);\n                            console.log('Fetching items for invoice ID:', invoiceId);\n                            const response = await fetch(\"/api/admin/invoices/\".concat(invoiceId, \"/items\"));\n                            console.log('Items fetch response status:', response.status, response.statusText);\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('Items fetch result:', result);\n                                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                                    // Parse Decimal.js objects with format {s, e, d}\n                                    const parseDecimal = {\n                                        \"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\": (decimalObj)=>{\n                                            if (!decimalObj || typeof decimalObj !== 'object') return 0;\n                                            const { s, e, d } = decimalObj;\n                                            if (s === undefined || e === undefined || !Array.isArray(d)) return 0;\n                                            // Convert digits array to number\n                                            const digits = d.join('');\n                                            if (!digits) return 0;\n                                            // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)\n                                            const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1);\n                                            return isNaN(value) ? 0 : value;\n                                        }\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\"];\n                                    // Map database items to form items\n                                    const mappedItems = result.data.map({\n                                        \"InvoiceModal.useEffect.loadInvoiceData.mappedItems\": (item)=>({\n                                                id: String(item.id),\n                                                description: String(item.description || ''),\n                                                quantity: parseDecimal(item.quantity),\n                                                unitPrice: parseDecimal(item.unitprice),\n                                                totalPrice: parseDecimal(item.totalprice)\n                                            })\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.mappedItems\"]);\n                                    console.log('Mapped items:', mappedItems);\n                                    setItems(mappedItems);\n                                } else {\n                                    // No items found, use default empty item\n                                    console.log('No items found in response, using default empty item');\n                                    setItems([\n                                        {\n                                            id: 'temp-default',\n                                            description: '',\n                                            quantity: 1,\n                                            unitPrice: 0,\n                                            totalPrice: 0\n                                        }\n                                    ]);\n                                }\n                            } else {\n                                // API error, use default empty item\n                                console.error('Failed to fetch invoice items:', response.status, response.statusText);\n                                setItems([\n                                    {\n                                        id: 'temp-default',\n                                        description: '',\n                                        quantity: 1,\n                                        unitPrice: 0,\n                                        totalPrice: 0\n                                    }\n                                ]);\n                            }\n                        } catch (error) {\n                            // Network or other error, use default empty item\n                            console.error('Error loading invoice items:', error);\n                            setItems([\n                                {\n                                    id: 'temp-default',\n                                    description: '',\n                                    quantity: 1,\n                                    unitPrice: 0,\n                                    totalPrice: 0\n                                }\n                            ]);\n                        } finally{\n                            setItemsLoading(false);\n                        }\n                    } else {\n                        // Reset form for new invoice\n                        setFormData({\n                            invoiceNumber: '',\n                            dueDate: '',\n                            status: 'DRAFT',\n                            description: '',\n                            taxRate: 0,\n                            subtotal: 0,\n                            taxAmount: 0,\n                            totalAmount: 0,\n                            paidAt: ''\n                        });\n                        setItems([\n                            {\n                                id: 'temp-default',\n                                description: '',\n                                quantity: 1,\n                                unitPrice: 0,\n                                totalPrice: 0\n                            }\n                        ]);\n                    }\n                }\n            }[\"InvoiceModal.useEffect.loadInvoiceData\"];\n            if (isOpen) {\n                loadInvoiceData();\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        initialData,\n        isOpen\n    ]);\n    const addItem = ()=>{\n        setItems([\n            ...items,\n            {\n                id: \"temp-\".concat(Date.now()),\n                description: '',\n                quantity: 1,\n                unitPrice: 0,\n                totalPrice: 0\n            }\n        ]);\n    };\n    const removeItem = (index)=>{\n        if (items.length > 1) {\n            setItems(items.filter((_, i)=>i !== index));\n        }\n    };\n    const updateItem = (index, field, value)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems[index] = {\n            ...updatedItems[index],\n            [field]: value\n        };\n        // Auto-calculate total price when quantity or unit price changes\n        if (field === 'quantity' || field === 'unitPrice') {\n            const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity;\n            const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice;\n            updatedItems[index].totalPrice = quantity * unitPrice;\n        }\n        setItems(updatedItems);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            // Validate items\n            const validItems = items.filter((item)=>item.description.trim() !== '');\n            if (validItems.length === 0) {\n                alert('Please add at least one item to the invoice.');\n                setLoading(false);\n                return;\n            }\n            // Validate that all items have positive whole number quantities and prices\n            const invalidItems = validItems.filter((item)=>{\n                // Handle floating-point precision issues by checking if quantity is close to an integer\n                const roundedQuantity = Math.round(item.quantity);\n                const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001;\n                const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger);\n                const isPriceValid = item.unitPrice >= 0;\n                if (!isQuantityValid || !isPriceValid) {\n                    console.log('Invalid item:', {\n                        description: item.description,\n                        originalQuantity: item.quantity,\n                        roundedQuantity: roundedQuantity,\n                        quantityType: typeof item.quantity,\n                        isInteger: Number.isInteger(item.quantity),\n                        isCloseToInteger: isCloseToInteger,\n                        unitPrice: item.unitPrice,\n                        isQuantityValid,\n                        isPriceValid\n                    });\n                }\n                return !isQuantityValid || !isPriceValid;\n            });\n            if (invalidItems.length > 0) {\n                console.log('Invalid items found:', invalidItems);\n                alert('All items must have positive whole number quantities and non-negative unit prices.');\n                setLoading(false);\n                return;\n            }\n            const submitData = {\n                ...formData,\n                items: validItems.map((item)=>{\n                    var _item_id;\n                    return {\n                        ...item,\n                        // Round quantity to handle floating-point precision issues when submitting\n                        quantity: Math.round(item.quantity),\n                        // Remove temporary IDs for new items\n                        id: ((_item_id = item.id) === null || _item_id === void 0 ? void 0 : _item_id.startsWith('temp-')) ? undefined : item.id\n                    };\n                }),\n                clientId: client.id,\n                projectId: project.id\n            };\n            console.log('Submitting invoice data:', submitData);\n            console.log('Valid items:', validItems);\n            await onSubmit(submitData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting invoice:', error);\n            alert('Failed to save invoice. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDownloadPDF = async ()=>{\n        try {\n            console.log('Starting PDF download with data:', {\n                formData,\n                client,\n                project\n            });\n            // Try basic HTML-to-PDF approach first\n            let response = await fetch('/api/invoices/generate-pdf-basic', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Basic PDF API response status:', response.status);\n            if (response.ok) {\n                // Open the HTML in a new window for printing/saving as PDF\n                const htmlContent = await response.text();\n                const printWindow = window.open('', '_blank');\n                if (printWindow) {\n                    printWindow.document.write(htmlContent);\n                    printWindow.document.close();\n                    printWindow.focus();\n                    // The HTML includes auto-print script, so it will automatically open print dialog\n                    console.log('PDF download completed successfully via HTML');\n                    return;\n                }\n            }\n            // If basic approach fails, try simple PDF generation\n            console.log('Basic PDF failed, trying simple PDF approach...');\n            response = await fetch('/api/invoices/generate-pdf-simple', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Simple PDF API response status:', response.status);\n            // If simple PDF fails, try the original Puppeteer approach\n            if (!response.ok) {\n                console.log('Simple PDF failed, trying Puppeteer approach...');\n                response = await fetch('/api/invoices/generate-pdf', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter((item)=>item.description.trim() !== '')\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Puppeteer PDF API response status:', response.status);\n            }\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                console.error('PDF generation failed:', errorData);\n                throw new Error(\"Failed to generate PDF: \".concat(errorData.details || errorData.error || 'Unknown error'));\n            }\n            const blob = await response.blob();\n            console.log('PDF blob created, size:', blob.size);\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"invoice-\".concat(formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''), \".pdf\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('PDF download completed successfully');\n        } catch (error) {\n            console.error('Download error:', error);\n            alert(\"PDF generation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"5bfaf54eaea5fa1d\",\n                children: \".modal-container.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important;min-height:auto!important}.modal-content-body.jsx-5bfaf54eaea5fa1d{overflow:visible!important;max-height:none!important;height:auto!important}.modal-content.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                title: title,\n                subtitle: \"\".concat(client.companyName, \" - \").concat(project.name),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 15\n                }, void 0),\n                iconColor: \"blue\",\n                disableOverlayClick: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        onClick: (e)=>e.stopPropagation(),\n                        style: {\n                            paddingBottom: '80px',\n                            height: 'auto',\n                            maxHeight: 'none',\n                            overflow: 'visible'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"modal-form-section-icon sample-style\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Invoice Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-4 gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Invoice Number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            invoiceNumber: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Auto-generated\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: [\n                                                                        \"Due Date \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-required\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 652,\n                                                                            columnNumber: 66\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"date\",\n                                                                    required: true,\n                                                                    value: formData.dueDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            dueDate: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.status,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            status: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-select\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DRAFT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Draft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"SENT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Sent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"PAID\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Paid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"OVERDUE\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Overdue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Tax Rate (%)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    max: \"100\",\n                                                                    step: \"0.01\",\n                                                                    value: formData.taxRate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            taxRate: parseFloat(e.target.value) || 0\n                                                                        }),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.description,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    description: e.target.value\n                                                                }),\n                                                            rows: 2,\n                                                            placeholder: \"Invoice description...\",\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-textarea\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"modal-form-section-icon sample-style green\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                \"Invoice Items (\",\n                                                                items.filter((item)=>item.description.trim() !== '').length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addItem,\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    \"Add Item\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        maxHeight: 'none',\n                                                        minHeight: '200px',\n                                                        overflow: 'visible'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 pl-2 border-r border-gray-300\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Qty\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Total\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 text-center\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        itemsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"p-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center space-x-2 text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm\",\n                                                                        children: \"Loading...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 17\n                                                        }, this) : items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors \".concat(index < items.length - 1 ? 'border-b border-gray-100' : ''),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: item.description,\n                                                                            onChange: (e)=>updateItem(index, 'description', e.target.value),\n                                                                            placeholder: \"Item description\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 \".concat(item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"1\",\n                                                                            step: \"1\",\n                                                                            value: item.quantity || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseInt(e.target.value) || 0;\n                                                                                updateItem(index, 'quantity', value);\n                                                                            },\n                                                                            placeholder: \"1\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.quantity <= 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 767,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            value: item.unitPrice || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseFloat(e.target.value) || 0;\n                                                                                updateItem(index, 'unitPrice', value);\n                                                                            },\n                                                                            placeholder: \"0.00\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (item.totalPrice || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>removeItem(index),\n                                                                            disabled: items.length === 1,\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 810,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id || \"item-\".concat(index), true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 13\n                                                }, this),\n                                                items.filter((item)=>item.description.trim() !== '').length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message warning\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon warning\",\n                                                                children: \"⚠️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text warning\",\n                                                                children: \"Please add at least one item to the invoice.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this),\n                                                items.some((item)=>item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message error\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon error\",\n                                                                children: \"❌\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text error\",\n                                                                children: \"All items must have positive whole number quantities and non-negative unit prices.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"modal-form-section-icon sample-style orange\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Invoice Summary\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Subtotal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.subtotal.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Tax (\",\n                                                                    formData.taxRate,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.taxAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Total Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-blue-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.totalAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Payment Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Amount Paid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-green-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 887,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Balance Due:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 892,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-red-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0)).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"mt-2\",\n                                                                children: formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 900,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Paid in Full\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this) : ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-yellow-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Partially Paid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Unpaid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (initialData === null || initialData === void 0 ? void 0 : initialData.payments) && initialData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Recent Payments\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-1\",\n                                                                children: [\n                                                                    initialData.payments.slice(0, 3).map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-gray-600\",\n                                                                                    children: new Date(payment.paymentDate).toLocaleDateString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 924,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"font-medium text-green-600\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        payment.amount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 925,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 923,\n                                                                            columnNumber: 27\n                                                                        }, this)),\n                                                                    initialData.payments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs text-gray-500 text-center pt-1\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            initialData.payments.length - 3,\n                                                                            \" more payments\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            padding: '24px 24px 0 24px',\n                            borderTop: '1px solid #e2e8f0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            position: 'absolute',\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            minHeight: '60px',\n                            opacity: 1,\n                            transform: 'none',\n                            borderBottomLeftRadius: '12px',\n                            borderBottomRightRadius: '12px'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px',\n                                    color: '#64748b',\n                                    fontSize: '14px',\n                                    position: 'absolute',\n                                    left: '24px',\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        style: {\n                                            width: '16px',\n                                            height: '16px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '12px',\n                                    position: 'absolute',\n                                    left: '50%',\n                                    top: '50%',\n                                    transform: 'translate(-50%, -50%)',\n                                    zIndex: 11\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleDownloadPDF,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none',\n                                            marginRight: '12px',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '4px'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                style: {\n                                                    width: '12px',\n                                                    height: '12px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View / Print\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        onClick: handleSubmit,\n                                        disabled: loading,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#3b82f6',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: loading ? 'not-allowed' : 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                                            transform: 'none',\n                                            opacity: loading ? 0.6 : 1\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#2563eb';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#3b82f6';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';\n                                            }\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '12px',\n                                                        height: '12px',\n                                                        border: '2px solid transparent',\n                                                        borderTopColor: 'currentColor',\n                                                        borderRadius: '50%',\n                                                        animation: 'spin 1s linear infinite'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\",\n                                                    children: \"Processing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 17\n                                        }, this) : initialData ? 'Update Invoice' : 'Create Invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 978,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 942,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"oZf/optZkqLL55/AMiQKF2LgdL4=\");\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/main.css":
/*!*****************************!*\
  !*** ./src/styles/main.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fc839db088d9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvbWFpbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvc3R5bGVzL21haW4uY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmM4MzlkYjA4OGQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/main.css\n"));

/***/ })

});