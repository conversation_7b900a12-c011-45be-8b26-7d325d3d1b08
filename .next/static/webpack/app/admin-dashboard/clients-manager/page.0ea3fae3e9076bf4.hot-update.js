"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/admin/clients/invoice-form-modal.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvoiceModal: () => (/* binding */ InvoiceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _shared_modal_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/modal-system */ \"(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\");\n/* __next_internal_client_entry_do_not_use__ InvoiceModal auto */ \nvar _s = $RefreshSig$();\n\n// Imports\n\n\n\n\n\n\n// ========================================\n// MAIN COMPONENT\n// ========================================\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSubmit, title, initialData, client, project } = param;\n    _s();\n    // ========================================\n    // STATE MANAGEMENT\n    // ========================================\n    // Loading states for different operations\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [itemsLoading, setItemsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [autoHeight, setAutoHeight] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Form data state - contains all invoice fields\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        invoiceNumber: '',\n        dueDate: '',\n        status: 'DRAFT',\n        description: '',\n        taxRate: 0,\n        subtotal: 0,\n        taxAmount: 0,\n        totalAmount: 0,\n        paidAt: ''\n    });\n    // Invoice items state - array of line items\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: 'temp-default',\n            description: '',\n            quantity: 1,\n            unitPrice: 0,\n            totalPrice: 0\n        }\n    ]);\n    // ========================================\n    // EFFECTS\n    // ========================================\n    // Calculate totals when items or tax rate changes - memoized for performance\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const subtotal = items.reduce({\n                \"InvoiceModal.useEffect.subtotal\": (sum, item)=>sum + item.totalPrice\n            }[\"InvoiceModal.useEffect.subtotal\"], 0);\n            const taxAmount = subtotal * formData.taxRate / 100;\n            const totalAmount = subtotal + taxAmount;\n            setFormData({\n                \"InvoiceModal.useEffect\": (prev)=>({\n                        ...prev,\n                        subtotal,\n                        taxAmount,\n                        totalAmount\n                    })\n            }[\"InvoiceModal.useEffect\"]);\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        formData.taxRate\n    ]);\n    // Auto-height logic for items container - ensures all items are visible\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            // Always keep auto-height enabled to show all items\n            setAutoHeight(true);\n            // Trigger auto-resize when items change\n            if (isOpen) {\n                const triggerResize = {\n                    \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Force a reflow and resize\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.triggerResize\"], 10);\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.triggerResize\"];\n                // Trigger resize after items change\n                setTimeout(triggerResize, 50);\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        isOpen\n    ]);\n    // Auto-resize modal based on content - fully automatic with observers\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const resizeModalToContent = {\n                    \"InvoiceModal.useEffect.resizeModalToContent\": ()=>{\n                        // Find the modal container\n                        const modalContainer = document.querySelector('.modal-container');\n                        const modalContent = document.querySelector('.modal-content-body');\n                        if (modalContainer && modalContent) {\n                            // Remove all height restrictions\n                            modalContainer.style.height = 'auto';\n                            modalContainer.style.maxHeight = 'none';\n                            modalContainer.style.minHeight = 'auto';\n                            modalContent.style.overflow = 'visible';\n                            modalContent.style.maxHeight = 'none';\n                            modalContent.style.height = 'auto';\n                            // Force a reflow to get accurate measurements\n                            modalContainer.offsetHeight;\n                            // Get the natural content height\n                            const contentHeight = modalContainer.scrollHeight;\n                            // Apply bounds but allow natural sizing\n                            const minHeight = 400;\n                            const maxHeight = window.innerHeight - 20;\n                            const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));\n                            // Apply the calculated height\n                            modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                            modalContainer.style.maxHeight = \"\".concat(maxHeight, \"px\");\n                            modalContent.style.overflow = 'visible';\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.resizeModalToContent\"];\n                // Immediate resize\n                resizeModalToContent();\n                // Resize after content is rendered\n                const timer = setTimeout(resizeModalToContent, 100);\n                // Resize when items change\n                const resizeTimer = setTimeout(resizeModalToContent, 50);\n                // Use ResizeObserver for real-time content changes\n                let resizeObserver = null;\n                const modalContent = document.querySelector('.modal-content-body');\n                if (modalContent && window.ResizeObserver) {\n                    resizeObserver = new ResizeObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    resizeObserver.observe(modalContent);\n                }\n                // Also observe the items container for changes\n                const itemsContainer = document.querySelector('.bg-white.border.border-gray-200.rounded-lg');\n                if (itemsContainer && window.ResizeObserver) {\n                    resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.observe(itemsContainer);\n                }\n                // Use MutationObserver to detect any DOM changes\n                let mutationObserver = null;\n                if (window.MutationObserver) {\n                    mutationObserver = new MutationObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    const modalContent = document.querySelector('.modal-content-body');\n                    if (modalContent) {\n                        mutationObserver.observe(modalContent, {\n                            childList: true,\n                            subtree: true,\n                            attributes: true,\n                            attributeFilter: [\n                                'style',\n                                'class'\n                            ]\n                        });\n                    }\n                }\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>{\n                        clearTimeout(timer);\n                        clearTimeout(resizeTimer);\n                        if (resizeObserver) {\n                            resizeObserver.disconnect();\n                        }\n                        if (mutationObserver) {\n                            mutationObserver.disconnect();\n                        }\n                    }\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen,\n        items\n    ]);\n    // Handle window resize to maintain proper modal sizing\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const handleWindowResize = {\n                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                    if (isOpen) {\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Auto-resize on window resize - ensures modal fits new viewport\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.handleWindowResize\"], 10);\n                        }\n                    }\n                }\n            }[\"InvoiceModal.useEffect.handleWindowResize\"];\n            window.addEventListener('resize', handleWindowResize);\n            return ({\n                \"InvoiceModal.useEffect\": ()=>window.removeEventListener('resize', handleWindowResize)\n            })[\"InvoiceModal.useEffect\"];\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Continuous auto-resize check for maximum automation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const continuousResize = {\n                    \"InvoiceModal.useEffect.continuousResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            const contentHeight = modalContainer.scrollHeight;\n                            const maxHeight = window.innerHeight - 20;\n                            const currentHeight = parseInt(modalContainer.style.height) || 0;\n                            const idealHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                            // Only resize if there's a significant difference - prevents unnecessary DOM updates\n                            if (Math.abs(currentHeight - idealHeight) > 10) {\n                                modalContainer.style.height = \"\".concat(idealHeight, \"px\");\n                            }\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.continuousResize\"];\n                // Check every 500ms for any changes - fallback for edge cases\n                const interval = setInterval(continuousResize, 500);\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>clearInterval(interval)\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Load initial data when modal opens with existing invoice\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const loadInvoiceData = {\n                \"InvoiceModal.useEffect.loadInvoiceData\": async ()=>{\n                    if (initialData) {\n                        // Populate form with existing invoice data - handles date formatting\n                        setFormData({\n                            invoiceNumber: initialData.invoiceNumber || '',\n                            dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',\n                            status: initialData.status || 'DRAFT',\n                            description: initialData.description || '',\n                            taxRate: Number(initialData.taxRate) || 0,\n                            subtotal: Number(initialData.subtotal) || 0,\n                            taxAmount: Number(initialData.taxAmount) || 0,\n                            totalAmount: Number(initialData.totalAmount) || 0,\n                            paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''\n                        });\n                        // Load existing items from API - handles Decimal.js serialization\n                        try {\n                            setItemsLoading(true);\n                            const invoiceId = String(initialData.id);\n                            console.log('Fetching items for invoice ID:', invoiceId);\n                            const response = await fetch(\"/api/admin/invoices/\".concat(invoiceId, \"/items\"));\n                            console.log('Items fetch response status:', response.status, response.statusText);\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('Items fetch result:', result);\n                                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                                    // Parse Decimal.js objects with format {s, e, d} - handles API serialization\n                                    const parseDecimal = {\n                                        \"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\": (decimalObj)=>{\n                                            if (!decimalObj || typeof decimalObj !== 'object') return 0;\n                                            const { s, e, d } = decimalObj;\n                                            if (s === undefined || e === undefined || !Array.isArray(d)) return 0;\n                                            // Convert digits array to number - Decimal.js internal format\n                                            const digits = d.join('');\n                                            if (!digits) return 0;\n                                            // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)\n                                            const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1);\n                                            return isNaN(value) ? 0 : value;\n                                        }\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\"];\n                                    // Map database items to form items\n                                    const mappedItems = result.data.map({\n                                        \"InvoiceModal.useEffect.loadInvoiceData.mappedItems\": (item)=>({\n                                                id: String(item.id),\n                                                description: String(item.description || ''),\n                                                quantity: parseDecimal(item.quantity),\n                                                unitPrice: parseDecimal(item.unitprice),\n                                                totalPrice: parseDecimal(item.totalprice)\n                                            })\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.mappedItems\"]);\n                                    console.log('Mapped items:', mappedItems);\n                                    setItems(mappedItems);\n                                } else {\n                                    // No items found, use default empty item\n                                    console.log('No items found in response, using default empty item');\n                                    setItems([\n                                        {\n                                            id: 'temp-default',\n                                            description: '',\n                                            quantity: 1,\n                                            unitPrice: 0,\n                                            totalPrice: 0\n                                        }\n                                    ]);\n                                }\n                            } else {\n                                // API error, use default empty item\n                                console.error('Failed to fetch invoice items:', response.status, response.statusText);\n                                setItems([\n                                    {\n                                        id: 'temp-default',\n                                        description: '',\n                                        quantity: 1,\n                                        unitPrice: 0,\n                                        totalPrice: 0\n                                    }\n                                ]);\n                            }\n                        } catch (error) {\n                            // Network or other error, use default empty item\n                            console.error('Error loading invoice items:', error);\n                            setItems([\n                                {\n                                    id: 'temp-default',\n                                    description: '',\n                                    quantity: 1,\n                                    unitPrice: 0,\n                                    totalPrice: 0\n                                }\n                            ]);\n                        } finally{\n                            setItemsLoading(false);\n                        }\n                    } else {\n                        // Reset form for new invoice\n                        setFormData({\n                            invoiceNumber: '',\n                            dueDate: '',\n                            status: 'DRAFT',\n                            description: '',\n                            taxRate: 0,\n                            subtotal: 0,\n                            taxAmount: 0,\n                            totalAmount: 0,\n                            paidAt: ''\n                        });\n                        setItems([\n                            {\n                                id: 'temp-default',\n                                description: '',\n                                quantity: 1,\n                                unitPrice: 0,\n                                totalPrice: 0\n                            }\n                        ]);\n                    }\n                }\n            }[\"InvoiceModal.useEffect.loadInvoiceData\"];\n            if (isOpen) {\n                loadInvoiceData();\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        initialData,\n        isOpen\n    ]);\n    // Add new invoice item - creates temporary ID for new items\n    const addItem = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[addItem]\": ()=>{\n            setItems([\n                ...items,\n                {\n                    id: \"temp-\".concat(Date.now()),\n                    description: '',\n                    quantity: 1,\n                    unitPrice: 0,\n                    totalPrice: 0\n                }\n            ]);\n        }\n    }[\"InvoiceModal.useCallback[addItem]\"], [\n        items\n    ]);\n    const removeItem = (index)=>{\n        if (items.length > 1) {\n            setItems(items.filter((_, i)=>i !== index));\n        }\n    };\n    const updateItem = (index, field, value)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems[index] = {\n            ...updatedItems[index],\n            [field]: value\n        };\n        // Auto-calculate total price when quantity or unit price changes\n        if (field === 'quantity' || field === 'unitPrice') {\n            const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity;\n            const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice;\n            updatedItems[index].totalPrice = quantity * unitPrice;\n        }\n        setItems(updatedItems);\n    };\n    // ======================================== // HANDLERS // ========================================\n    // Handle form submission - creates or updates invoice with validation\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[handleSubmit]\": async (e)=>{\n            e.preventDefault();\n            setLoading(true);\n            try {\n                // Validate items\n                const validItems = items.filter({\n                    \"InvoiceModal.useCallback[handleSubmit].validItems\": (item)=>item.description.trim() !== ''\n                }[\"InvoiceModal.useCallback[handleSubmit].validItems\"]);\n                if (validItems.length === 0) {\n                    alert('Please add at least one item to the invoice.');\n                    setLoading(false);\n                    return;\n                }\n                // Validate that all items have positive whole number quantities and prices\n                const invalidItems = validItems.filter({\n                    \"InvoiceModal.useCallback[handleSubmit].invalidItems\": (item)=>{\n                        // Handle floating-point precision issues by checking if quantity is close to an integer\n                        const roundedQuantity = Math.round(item.quantity);\n                        const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001;\n                        const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger);\n                        const isPriceValid = item.unitPrice >= 0;\n                        if (!isQuantityValid || !isPriceValid) {\n                            console.log('Invalid item:', {\n                                description: item.description,\n                                originalQuantity: item.quantity,\n                                roundedQuantity: roundedQuantity,\n                                quantityType: typeof item.quantity,\n                                isInteger: Number.isInteger(item.quantity),\n                                isCloseToInteger: isCloseToInteger,\n                                unitPrice: item.unitPrice,\n                                isQuantityValid,\n                                isPriceValid\n                            });\n                        }\n                        return !isQuantityValid || !isPriceValid;\n                    }\n                }[\"InvoiceModal.useCallback[handleSubmit].invalidItems\"]);\n                if (invalidItems.length > 0) {\n                    console.log('Invalid items found:', invalidItems);\n                    alert('All items must have positive whole number quantities and non-negative unit prices.');\n                    setLoading(false);\n                    return;\n                }\n                const submitData = {\n                    ...formData,\n                    items: validItems.map({\n                        \"InvoiceModal.useCallback[handleSubmit]\": (item)=>{\n                            var _item_id;\n                            return {\n                                ...item,\n                                // Round quantity to handle floating-point precision issues when submitting\n                                quantity: Math.round(item.quantity),\n                                // Remove temporary IDs for new items\n                                id: ((_item_id = item.id) === null || _item_id === void 0 ? void 0 : _item_id.startsWith('temp-')) ? undefined : item.id\n                            };\n                        }\n                    }[\"InvoiceModal.useCallback[handleSubmit]\"]),\n                    clientId: client.id,\n                    projectId: project.id\n                };\n                console.log('Submitting invoice data:', submitData);\n                console.log('Valid items:', validItems);\n                await onSubmit(submitData);\n                onClose();\n            } catch (error) {\n                console.error('Error submitting invoice:', error);\n                alert('Failed to save invoice. Please try again.');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"InvoiceModal.useCallback[handleSubmit]\"], [\n        formData,\n        items,\n        client,\n        project,\n        onSubmit,\n        onClose\n    ]);\n    // Handle PDF generation and download - tries multiple PDF generation methods\n    const handleDownloadPDF = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[handleDownloadPDF]\": async ()=>{\n            try {\n                console.log('Starting PDF download with data:', {\n                    formData,\n                    client,\n                    project\n                });\n                // Try basic HTML-to-PDF approach first\n                let response = await fetch('/api/invoices/generate-pdf-basic', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter({\n                                \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                            }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Basic PDF API response status:', response.status);\n                if (response.ok) {\n                    // Open the HTML in a new window for printing/saving as PDF\n                    const htmlContent = await response.text();\n                    const printWindow = window.open('', '_blank');\n                    if (printWindow) {\n                        printWindow.document.write(htmlContent);\n                        printWindow.document.close();\n                        printWindow.focus();\n                        // The HTML includes auto-print script, so it will automatically open print dialog\n                        console.log('PDF download completed successfully via HTML');\n                        return;\n                    }\n                }\n                // If basic approach fails, try simple PDF generation\n                console.log('Basic PDF failed, trying simple PDF approach...');\n                response = await fetch('/api/invoices/generate-pdf-simple', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter({\n                                \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                            }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Simple PDF API response status:', response.status);\n                // If simple PDF fails, try the original Puppeteer approach\n                if (!response.ok) {\n                    console.log('Simple PDF failed, trying Puppeteer approach...');\n                    response = await fetch('/api/invoices/generate-pdf', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            invoiceData: {\n                                invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                dueDate: formData.dueDate,\n                                status: formData.status,\n                                description: formData.description,\n                                taxRate: formData.taxRate,\n                                subtotal: formData.subtotal,\n                                taxAmount: formData.taxAmount,\n                                totalAmount: formData.totalAmount,\n                                paidAt: formData.paidAt,\n                                items: items.filter({\n                                    \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                                }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                            },\n                            client,\n                            project\n                        })\n                    });\n                    console.log('Puppeteer PDF API response status:', response.status);\n                }\n                if (!response.ok) {\n                    const errorData = await response.json().catch({\n                        \"InvoiceModal.useCallback[handleDownloadPDF]\": ()=>({})\n                    }[\"InvoiceModal.useCallback[handleDownloadPDF]\"]);\n                    console.error('PDF generation failed:', errorData);\n                    throw new Error(\"Failed to generate PDF: \".concat(errorData.details || errorData.error || 'Unknown error'));\n                }\n                const blob = await response.blob();\n                console.log('PDF blob created, size:', blob.size);\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = \"invoice-\".concat(formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''), \".pdf\");\n                document.body.appendChild(a);\n                a.click();\n                window.URL.revokeObjectURL(url);\n                document.body.removeChild(a);\n                console.log('PDF download completed successfully');\n            } catch (error) {\n                console.error('Download error:', error);\n                alert(\"PDF generation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n    }[\"InvoiceModal.useCallback[handleDownloadPDF]\"], [\n        formData,\n        items,\n        client,\n        project\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"5bfaf54eaea5fa1d\",\n                children: \".modal-container.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important;min-height:auto!important}.modal-content-body.jsx-5bfaf54eaea5fa1d{overflow:visible!important;max-height:none!important;height:auto!important}.modal-content.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                title: title,\n                subtitle: \"\".concat(client.companyName, \" - \").concat(project.name),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                    lineNumber: 627,\n                    columnNumber: 15\n                }, void 0),\n                iconColor: \"blue\",\n                disableOverlayClick: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        onClick: (e)=>e.stopPropagation(),\n                        style: {\n                            paddingBottom: '80px',\n                            height: 'auto',\n                            maxHeight: 'none',\n                            overflow: 'visible'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"modal-form-section-icon sample-style\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Invoice Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-4 gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Invoice Number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            invoiceNumber: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Auto-generated\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: [\n                                                                        \"Due Date \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-required\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 66\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"date\",\n                                                                    required: true,\n                                                                    value: formData.dueDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            dueDate: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.status,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            status: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-select\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DRAFT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Draft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"SENT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Sent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 675,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"PAID\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Paid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"OVERDUE\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Overdue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Tax Rate (%)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    max: \"100\",\n                                                                    step: \"0.01\",\n                                                                    value: formData.taxRate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            taxRate: parseFloat(e.target.value) || 0\n                                                                        }),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.description,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    description: e.target.value\n                                                                }),\n                                                            rows: 2,\n                                                            placeholder: \"Invoice description...\",\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-textarea\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"modal-form-section-icon sample-style green\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                \"Invoice Items (\",\n                                                                items.filter((item)=>item.description.trim() !== '').length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addItem,\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    \"Add Item\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        maxHeight: 'none',\n                                                        minHeight: '200px',\n                                                        overflow: 'visible'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 pl-2 border-r border-gray-300\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Qty\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Total\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 text-center\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        itemsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"p-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center space-x-2 text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm\",\n                                                                        children: \"Loading...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 17\n                                                        }, this) : items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors \".concat(index < items.length - 1 ? 'border-b border-gray-100' : ''),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: item.description,\n                                                                            onChange: (e)=>updateItem(index, 'description', e.target.value),\n                                                                            placeholder: \"Item description\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 \".concat(item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"1\",\n                                                                            step: \"1\",\n                                                                            value: item.quantity || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseInt(e.target.value) || 0;\n                                                                                updateItem(index, 'quantity', value);\n                                                                            },\n                                                                            placeholder: \"1\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.quantity <= 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            value: item.unitPrice || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseFloat(e.target.value) || 0;\n                                                                                updateItem(index, 'unitPrice', value);\n                                                                            },\n                                                                            placeholder: \"0.00\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (item.totalPrice || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>removeItem(index),\n                                                                            disabled: items.length === 1,\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                lineNumber: 822,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 815,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id || \"item-\".concat(index), true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 13\n                                                }, this),\n                                                items.filter((item)=>item.description.trim() !== '').length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message warning\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon warning\",\n                                                                children: \"⚠️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text warning\",\n                                                                children: \"Please add at least one item to the invoice.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 19\n                                                }, this),\n                                                items.some((item)=>item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message error\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon error\",\n                                                                children: \"❌\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text error\",\n                                                                children: \"All items must have positive whole number quantities and non-negative unit prices.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"modal-form-section-icon sample-style orange\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Invoice Summary\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Subtotal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.subtotal.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Tax (\",\n                                                                    formData.taxRate,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.taxAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Total Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-blue-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.totalAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Payment Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Amount Paid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 892,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-green-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Balance Due:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 898,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-red-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0)).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 899,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"mt-2\",\n                                                                children: formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Paid in Full\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, this) : ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-yellow-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Partially Paid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 916,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Unpaid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (initialData === null || initialData === void 0 ? void 0 : initialData.payments) && initialData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Recent Payments\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-1\",\n                                                                children: [\n                                                                    initialData.payments.slice(0, 3).map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-gray-600\",\n                                                                                    children: new Date(payment.paymentDate).toLocaleDateString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 930,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"font-medium text-green-600\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        payment.amount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 931,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 929,\n                                                                            columnNumber: 27\n                                                                        }, this)),\n                                                                    initialData.payments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs text-gray-500 text-center pt-1\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            initialData.payments.length - 3,\n                                                                            \" more payments\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 935,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            padding: '24px 24px 0 24px',\n                            borderTop: '1px solid #e2e8f0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            position: 'absolute',\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            minHeight: '60px',\n                            opacity: 1,\n                            transform: 'none',\n                            borderBottomLeftRadius: '12px',\n                            borderBottomRightRadius: '12px'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px',\n                                    color: '#64748b',\n                                    fontSize: '14px',\n                                    position: 'absolute',\n                                    left: '24px',\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        style: {\n                                            width: '16px',\n                                            height: '16px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 968,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '12px',\n                                    position: 'absolute',\n                                    left: '50%',\n                                    top: '50%',\n                                    transform: 'translate(-50%, -50%)',\n                                    zIndex: 11\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleDownloadPDF,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none',\n                                            marginRight: '12px',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '4px'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                style: {\n                                                    width: '12px',\n                                                    height: '12px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View / Print\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        onClick: handleSubmit,\n                                        disabled: loading,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#3b82f6',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: loading ? 'not-allowed' : 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                                            transform: 'none',\n                                            opacity: loading ? 0.6 : 1\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#2563eb';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#3b82f6';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';\n                                            }\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '12px',\n                                                        height: '12px',\n                                                        border: '2px solid transparent',\n                                                        borderTopColor: 'currentColor',\n                                                        borderRadius: '50%',\n                                                        animation: 'spin 1s linear infinite'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\",\n                                                    children: \"Processing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1099,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 17\n                                        }, this) : initialData ? 'Update Invoice' : 'Create Invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                lineNumber: 622,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"FpJeJN5+nl08M0Me2vvmMH7/wPE=\");\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/main.css":
/*!*****************************!*\
  !*** ./src/styles/main.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2e37ad51a132\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvbWFpbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvc3R5bGVzL21haW4uY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmUzN2FkNTFhMTMyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/main.css\n"));

/***/ })

});