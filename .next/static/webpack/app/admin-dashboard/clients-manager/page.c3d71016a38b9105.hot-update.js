"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/admin/clients/invoice-form-modal.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvoiceModal: () => (/* binding */ InvoiceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _shared_modal_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/modal-system */ \"(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\");\n/* __next_internal_client_entry_do_not_use__ InvoiceModal auto */ \nvar _s = $RefreshSig$();\n\n// Imports\n\n\n\n\n\n\n// ========================================\n// MAIN COMPONENT\n// ========================================\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSubmit, title, initialData, client, project } = param;\n    _s();\n    // ========================================\n    // STATE MANAGEMENT\n    // ========================================\n    // Loading states for different operations\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [itemsLoading, setItemsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [autoHeight, setAutoHeight] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Form data state - contains all invoice fields\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        invoiceNumber: '',\n        dueDate: '',\n        status: 'DRAFT',\n        description: '',\n        taxRate: 0,\n        subtotal: 0,\n        taxAmount: 0,\n        totalAmount: 0,\n        paidAt: ''\n    });\n    // Invoice items state - array of line items\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: 'temp-default',\n            description: '',\n            quantity: 1,\n            unitPrice: 0,\n            totalPrice: 0\n        }\n    ]);\n    // ========================================\n    // EFFECTS\n    // ========================================\n    // Calculate totals when items or tax rate changes - memoized for performance\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const subtotal = items.reduce({\n                \"InvoiceModal.useEffect.subtotal\": (sum, item)=>sum + item.totalPrice\n            }[\"InvoiceModal.useEffect.subtotal\"], 0);\n            const taxAmount = subtotal * formData.taxRate / 100;\n            const totalAmount = subtotal + taxAmount;\n            setFormData({\n                \"InvoiceModal.useEffect\": (prev)=>({\n                        ...prev,\n                        subtotal,\n                        taxAmount,\n                        totalAmount\n                    })\n            }[\"InvoiceModal.useEffect\"]);\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        formData.taxRate\n    ]);\n    // Auto-height logic for items container - ensures all items are visible\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            // Always keep auto-height enabled to show all items\n            setAutoHeight(true);\n            // Trigger auto-resize when items change\n            if (isOpen) {\n                const triggerResize = {\n                    \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Force a reflow and resize\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.triggerResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.triggerResize\"], 10);\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.triggerResize\"];\n                // Trigger resize after items change\n                setTimeout(triggerResize, 50);\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        isOpen\n    ]);\n    // Auto-resize modal based on content - fully automatic with observers\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const resizeModalToContent = {\n                    \"InvoiceModal.useEffect.resizeModalToContent\": ()=>{\n                        // Find the modal container\n                        const modalContainer = document.querySelector('.modal-container');\n                        const modalContent = document.querySelector('.modal-content-body');\n                        if (modalContainer && modalContent) {\n                            // Remove all height restrictions\n                            modalContainer.style.height = 'auto';\n                            modalContainer.style.maxHeight = 'none';\n                            modalContainer.style.minHeight = 'auto';\n                            modalContent.style.overflow = 'visible';\n                            modalContent.style.maxHeight = 'none';\n                            modalContent.style.height = 'auto';\n                            // Force a reflow to get accurate measurements\n                            modalContainer.offsetHeight;\n                            // Get the natural content height\n                            const contentHeight = modalContainer.scrollHeight;\n                            // Apply bounds but allow natural sizing\n                            const minHeight = 400;\n                            const maxHeight = window.innerHeight - 20;\n                            const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));\n                            // Apply the calculated height\n                            modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                            modalContainer.style.maxHeight = \"\".concat(maxHeight, \"px\");\n                            modalContent.style.overflow = 'visible';\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.resizeModalToContent\"];\n                // Immediate resize\n                resizeModalToContent();\n                // Resize after content is rendered\n                const timer = setTimeout(resizeModalToContent, 100);\n                // Resize when items change\n                const resizeTimer = setTimeout(resizeModalToContent, 50);\n                // Use ResizeObserver for real-time content changes\n                let resizeObserver = null;\n                const modalContent = document.querySelector('.modal-content-body');\n                if (modalContent && window.ResizeObserver) {\n                    resizeObserver = new ResizeObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    resizeObserver.observe(modalContent);\n                }\n                // Also observe the items container for changes\n                const itemsContainer = document.querySelector('.bg-white.border.border-gray-200.rounded-lg');\n                if (itemsContainer && window.ResizeObserver) {\n                    resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.observe(itemsContainer);\n                }\n                // Use MutationObserver to detect any DOM changes\n                let mutationObserver = null;\n                if (window.MutationObserver) {\n                    mutationObserver = new MutationObserver({\n                        \"InvoiceModal.useEffect\": ()=>{\n                            resizeModalToContent();\n                        }\n                    }[\"InvoiceModal.useEffect\"]);\n                    const modalContent = document.querySelector('.modal-content-body');\n                    if (modalContent) {\n                        mutationObserver.observe(modalContent, {\n                            childList: true,\n                            subtree: true,\n                            attributes: true,\n                            attributeFilter: [\n                                'style',\n                                'class'\n                            ]\n                        });\n                    }\n                }\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>{\n                        clearTimeout(timer);\n                        clearTimeout(resizeTimer);\n                        if (resizeObserver) {\n                            resizeObserver.disconnect();\n                        }\n                        if (mutationObserver) {\n                            mutationObserver.disconnect();\n                        }\n                    }\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen,\n        items\n    ]);\n    // Handle window resize to maintain proper modal sizing\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const handleWindowResize = {\n                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                    if (isOpen) {\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            // Auto-resize on window resize - ensures modal fits new viewport\n                            modalContainer.style.height = 'auto';\n                            setTimeout({\n                                \"InvoiceModal.useEffect.handleWindowResize\": ()=>{\n                                    const contentHeight = modalContainer.scrollHeight;\n                                    const maxHeight = window.innerHeight - 20;\n                                    const finalHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                                    modalContainer.style.height = \"\".concat(finalHeight, \"px\");\n                                }\n                            }[\"InvoiceModal.useEffect.handleWindowResize\"], 10);\n                        }\n                    }\n                }\n            }[\"InvoiceModal.useEffect.handleWindowResize\"];\n            window.addEventListener('resize', handleWindowResize);\n            return ({\n                \"InvoiceModal.useEffect\": ()=>window.removeEventListener('resize', handleWindowResize)\n            })[\"InvoiceModal.useEffect\"];\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Continuous auto-resize check for maximum automation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            if (isOpen) {\n                const continuousResize = {\n                    \"InvoiceModal.useEffect.continuousResize\": ()=>{\n                        const modalContainer = document.querySelector('.modal-container');\n                        if (modalContainer) {\n                            const contentHeight = modalContainer.scrollHeight;\n                            const maxHeight = window.innerHeight - 20;\n                            const currentHeight = parseInt(modalContainer.style.height) || 0;\n                            const idealHeight = Math.min(maxHeight, Math.max(500, contentHeight));\n                            // Only resize if there's a significant difference - prevents unnecessary DOM updates\n                            if (Math.abs(currentHeight - idealHeight) > 10) {\n                                modalContainer.style.height = \"\".concat(idealHeight, \"px\");\n                            }\n                        }\n                    }\n                }[\"InvoiceModal.useEffect.continuousResize\"];\n                // Check every 500ms for any changes - fallback for edge cases\n                const interval = setInterval(continuousResize, 500);\n                return ({\n                    \"InvoiceModal.useEffect\": ()=>clearInterval(interval)\n                })[\"InvoiceModal.useEffect\"];\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        isOpen\n    ]);\n    // Load initial data when modal opens with existing invoice\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const loadInvoiceData = {\n                \"InvoiceModal.useEffect.loadInvoiceData\": async ()=>{\n                    if (initialData) {\n                        // Populate form with existing invoice data - handles date formatting\n                        setFormData({\n                            invoiceNumber: initialData.invoiceNumber || '',\n                            dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',\n                            status: initialData.status || 'DRAFT',\n                            description: initialData.description || '',\n                            taxRate: Number(initialData.taxRate) || 0,\n                            subtotal: Number(initialData.subtotal) || 0,\n                            taxAmount: Number(initialData.taxAmount) || 0,\n                            totalAmount: Number(initialData.totalAmount) || 0,\n                            paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''\n                        });\n                        // Load existing items from API - handles Decimal.js serialization\n                        try {\n                            setItemsLoading(true);\n                            const invoiceId = String(initialData.id);\n                            console.log('Fetching items for invoice ID:', invoiceId);\n                            const response = await fetch(\"/api/admin/invoices/\".concat(invoiceId, \"/items\"));\n                            console.log('Items fetch response status:', response.status, response.statusText);\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('Items fetch result:', result);\n                                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                                    // Parse Decimal.js objects with format {s, e, d} - handles API serialization\n                                    const parseDecimal = {\n                                        \"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\": (decimalObj)=>{\n                                            if (!decimalObj || typeof decimalObj !== 'object') return 0;\n                                            const { s, e, d } = decimalObj;\n                                            if (s === undefined || e === undefined || !Array.isArray(d)) return 0;\n                                            // Convert digits array to number - Decimal.js internal format\n                                            const digits = d.join('');\n                                            if (!digits) return 0;\n                                            // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)\n                                            const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1);\n                                            return isNaN(value) ? 0 : value;\n                                        }\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\"];\n                                    // Map database items to form items\n                                    const mappedItems = result.data.map({\n                                        \"InvoiceModal.useEffect.loadInvoiceData.mappedItems\": (item)=>({\n                                                id: String(item.id),\n                                                description: String(item.description || ''),\n                                                quantity: parseDecimal(item.quantity),\n                                                unitPrice: parseDecimal(item.unitprice),\n                                                totalPrice: parseDecimal(item.totalprice)\n                                            })\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.mappedItems\"]);\n                                    console.log('Mapped items:', mappedItems);\n                                    setItems(mappedItems);\n                                } else {\n                                    // No items found, use default empty item\n                                    console.log('No items found in response, using default empty item');\n                                    setItems([\n                                        {\n                                            id: 'temp-default',\n                                            description: '',\n                                            quantity: 1,\n                                            unitPrice: 0,\n                                            totalPrice: 0\n                                        }\n                                    ]);\n                                }\n                            } else {\n                                // API error, use default empty item\n                                console.error('Failed to fetch invoice items:', response.status, response.statusText);\n                                setItems([\n                                    {\n                                        id: 'temp-default',\n                                        description: '',\n                                        quantity: 1,\n                                        unitPrice: 0,\n                                        totalPrice: 0\n                                    }\n                                ]);\n                            }\n                        } catch (error) {\n                            // Network or other error, use default empty item\n                            console.error('Error loading invoice items:', error);\n                            setItems([\n                                {\n                                    id: 'temp-default',\n                                    description: '',\n                                    quantity: 1,\n                                    unitPrice: 0,\n                                    totalPrice: 0\n                                }\n                            ]);\n                        } finally{\n                            setItemsLoading(false);\n                        }\n                    } else {\n                        // Reset form for new invoice\n                        setFormData({\n                            invoiceNumber: '',\n                            dueDate: '',\n                            status: 'DRAFT',\n                            description: '',\n                            taxRate: 0,\n                            subtotal: 0,\n                            taxAmount: 0,\n                            totalAmount: 0,\n                            paidAt: ''\n                        });\n                        setItems([\n                            {\n                                id: 'temp-default',\n                                description: '',\n                                quantity: 1,\n                                unitPrice: 0,\n                                totalPrice: 0\n                            }\n                        ]);\n                    }\n                }\n            }[\"InvoiceModal.useEffect.loadInvoiceData\"];\n            if (isOpen) {\n                loadInvoiceData();\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        initialData,\n        isOpen\n    ]);\n    // Add new invoice item - creates temporary ID for new items\n    const addItem = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[addItem]\": ()=>{\n            setItems([\n                ...items,\n                {\n                    id: \"temp-\".concat(Date.now()),\n                    description: '',\n                    quantity: 1,\n                    unitPrice: 0,\n                    totalPrice: 0\n                }\n            ]);\n        }\n    }[\"InvoiceModal.useCallback[addItem]\"], [\n        items\n    ]);\n    // Remove invoice item - prevents removing the last item\n    const removeItem = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[removeItem]\": (index)=>{\n            if (items.length > 1) {\n                setItems(items.filter({\n                    \"InvoiceModal.useCallback[removeItem]\": (_, i)=>i !== index\n                }[\"InvoiceModal.useCallback[removeItem]\"]));\n            }\n        }\n    }[\"InvoiceModal.useCallback[removeItem]\"], [\n        items\n    ]);\n    // Update invoice item - handles field updates and recalculates totals\n    const updateItem = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[updateItem]\": (index, field, value)=>{\n            const updatedItems = [\n                ...items\n            ];\n            updatedItems[index] = {\n                ...updatedItems[index],\n                [field]: value\n            };\n            // Auto-calculate total price when quantity or unit price changes\n            if (field === 'quantity' || field === 'unitPrice') {\n                const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity;\n                const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice;\n                updatedItems[index].totalPrice = quantity * unitPrice;\n            }\n            setItems(updatedItems);\n        }\n    }[\"InvoiceModal.useCallback[updateItem]\"], [\n        items\n    ]);\n    // ======================================== // HANDLERS // ========================================\n    // Handle form submission - creates or updates invoice with validation\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[handleSubmit]\": async (e)=>{\n            e.preventDefault();\n            setLoading(true);\n            try {\n                // Validate items\n                const validItems = items.filter({\n                    \"InvoiceModal.useCallback[handleSubmit].validItems\": (item)=>item.description.trim() !== ''\n                }[\"InvoiceModal.useCallback[handleSubmit].validItems\"]);\n                if (validItems.length === 0) {\n                    alert('Please add at least one item to the invoice.');\n                    setLoading(false);\n                    return;\n                }\n                // Validate that all items have positive whole number quantities and prices\n                const invalidItems = validItems.filter({\n                    \"InvoiceModal.useCallback[handleSubmit].invalidItems\": (item)=>{\n                        // Handle floating-point precision issues by checking if quantity is close to an integer\n                        const roundedQuantity = Math.round(item.quantity);\n                        const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001;\n                        const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger);\n                        const isPriceValid = item.unitPrice >= 0;\n                        if (!isQuantityValid || !isPriceValid) {\n                            console.log('Invalid item:', {\n                                description: item.description,\n                                originalQuantity: item.quantity,\n                                roundedQuantity: roundedQuantity,\n                                quantityType: typeof item.quantity,\n                                isInteger: Number.isInteger(item.quantity),\n                                isCloseToInteger: isCloseToInteger,\n                                unitPrice: item.unitPrice,\n                                isQuantityValid,\n                                isPriceValid\n                            });\n                        }\n                        return !isQuantityValid || !isPriceValid;\n                    }\n                }[\"InvoiceModal.useCallback[handleSubmit].invalidItems\"]);\n                if (invalidItems.length > 0) {\n                    console.log('Invalid items found:', invalidItems);\n                    alert('All items must have positive whole number quantities and non-negative unit prices.');\n                    setLoading(false);\n                    return;\n                }\n                const submitData = {\n                    ...formData,\n                    items: validItems.map({\n                        \"InvoiceModal.useCallback[handleSubmit]\": (item)=>{\n                            var _item_id;\n                            return {\n                                ...item,\n                                // Round quantity to handle floating-point precision issues when submitting\n                                quantity: Math.round(item.quantity),\n                                // Remove temporary IDs for new items\n                                id: ((_item_id = item.id) === null || _item_id === void 0 ? void 0 : _item_id.startsWith('temp-')) ? undefined : item.id\n                            };\n                        }\n                    }[\"InvoiceModal.useCallback[handleSubmit]\"]),\n                    clientId: client.id,\n                    projectId: project.id\n                };\n                console.log('Submitting invoice data:', submitData);\n                console.log('Valid items:', validItems);\n                await onSubmit(submitData);\n                onClose();\n            } catch (error) {\n                console.error('Error submitting invoice:', error);\n                alert('Failed to save invoice. Please try again.');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"InvoiceModal.useCallback[handleSubmit]\"], [\n        formData,\n        items,\n        client,\n        project,\n        onSubmit,\n        onClose\n    ]);\n    // Handle PDF generation and download - tries multiple PDF generation methods\n    const handleDownloadPDF = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"InvoiceModal.useCallback[handleDownloadPDF]\": async ()=>{\n            try {\n                console.log('Starting PDF download with data:', {\n                    formData,\n                    client,\n                    project\n                });\n                // Try basic HTML-to-PDF approach first\n                let response = await fetch('/api/invoices/generate-pdf-basic', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter({\n                                \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                            }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Basic PDF API response status:', response.status);\n                if (response.ok) {\n                    // Open the HTML in a new window for printing/saving as PDF\n                    const htmlContent = await response.text();\n                    const printWindow = window.open('', '_blank');\n                    if (printWindow) {\n                        printWindow.document.write(htmlContent);\n                        printWindow.document.close();\n                        printWindow.focus();\n                        // The HTML includes auto-print script, so it will automatically open print dialog\n                        console.log('PDF download completed successfully via HTML');\n                        return;\n                    }\n                }\n                // If basic approach fails, try simple PDF generation\n                console.log('Basic PDF failed, trying simple PDF approach...');\n                response = await fetch('/api/invoices/generate-pdf-simple', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter({\n                                \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                            }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Simple PDF API response status:', response.status);\n                // If simple PDF fails, try the original Puppeteer approach\n                if (!response.ok) {\n                    console.log('Simple PDF failed, trying Puppeteer approach...');\n                    response = await fetch('/api/invoices/generate-pdf', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            invoiceData: {\n                                invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                dueDate: formData.dueDate,\n                                status: formData.status,\n                                description: formData.description,\n                                taxRate: formData.taxRate,\n                                subtotal: formData.subtotal,\n                                taxAmount: formData.taxAmount,\n                                totalAmount: formData.totalAmount,\n                                paidAt: formData.paidAt,\n                                items: items.filter({\n                                    \"InvoiceModal.useCallback[handleDownloadPDF]\": (item)=>item.description.trim() !== ''\n                                }[\"InvoiceModal.useCallback[handleDownloadPDF]\"])\n                            },\n                            client,\n                            project\n                        })\n                    });\n                    console.log('Puppeteer PDF API response status:', response.status);\n                }\n                if (!response.ok) {\n                    const errorData = await response.json().catch({\n                        \"InvoiceModal.useCallback[handleDownloadPDF]\": ()=>({})\n                    }[\"InvoiceModal.useCallback[handleDownloadPDF]\"]);\n                    console.error('PDF generation failed:', errorData);\n                    throw new Error(\"Failed to generate PDF: \".concat(errorData.details || errorData.error || 'Unknown error'));\n                }\n                const blob = await response.blob();\n                console.log('PDF blob created, size:', blob.size);\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = \"invoice-\".concat(formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''), \".pdf\");\n                document.body.appendChild(a);\n                a.click();\n                window.URL.revokeObjectURL(url);\n                document.body.removeChild(a);\n                console.log('PDF download completed successfully');\n            } catch (error) {\n                console.error('Download error:', error);\n                alert(\"PDF generation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n    }[\"InvoiceModal.useCallback[handleDownloadPDF]\"], [\n        formData,\n        items,\n        client,\n        project\n    ]);\n    // ======================================== // RENDERING // ========================================\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"5bfaf54eaea5fa1d\",\n                children: \".modal-container.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important;min-height:auto!important}.modal-content-body.jsx-5bfaf54eaea5fa1d{overflow:visible!important;max-height:none!important;height:auto!important}.modal-content.jsx-5bfaf54eaea5fa1d{height:auto!important;max-height:none!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                title: title,\n                subtitle: \"\".concat(client.companyName, \" - \").concat(project.name),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 15\n                }, void 0),\n                iconColor: \"blue\",\n                disableOverlayClick: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        onClick: (e)=>e.stopPropagation(),\n                        style: {\n                            paddingBottom: '80px',\n                            height: 'auto',\n                            maxHeight: 'none',\n                            overflow: 'visible'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"modal-form-section-icon sample-style\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Invoice Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-4 gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Invoice Number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            invoiceNumber: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Auto-generated\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: [\n                                                                        \"Due Date \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-required\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 66\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"date\",\n                                                                    required: true,\n                                                                    value: formData.dueDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            dueDate: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.status,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            status: e.target.value\n                                                                        }),\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-select\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DRAFT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Draft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"SENT\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Sent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"PAID\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Paid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"OVERDUE\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                                                            children: \"Overdue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                                    children: \"Tax Rate (%)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    max: \"100\",\n                                                                    step: \"0.01\",\n                                                                    value: formData.taxRate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            taxRate: parseFloat(e.target.value) || 0\n                                                                        }),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-label\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.description,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    description: e.target.value\n                                                                }),\n                                                            rows: 2,\n                                                            placeholder: \"Invoice description...\",\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-textarea\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"modal-form-section-icon sample-style green\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                \"Invoice Items (\",\n                                                                items.filter((item)=>item.description.trim() !== '').length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addItem,\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    \"Add Item\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        maxHeight: 'none',\n                                                        minHeight: '200px',\n                                                        overflow: 'visible'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 pl-2 border-r border-gray-300\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Qty\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 text-center border-r border-gray-300\",\n                                                                    children: \"Total\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 text-center\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        itemsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"p-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center space-x-2 text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm\",\n                                                                        children: \"Loading...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 17\n                                                        }, this) : items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors \".concat(index < items.length - 1 ? 'border-b border-gray-100' : ''),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-5 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: item.description,\n                                                                            onChange: (e)=>updateItem(index, 'description', e.target.value),\n                                                                            placeholder: \"Item description\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 \".concat(item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"1\",\n                                                                            step: \"1\",\n                                                                            value: item.quantity || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseInt(e.target.value) || 0;\n                                                                                updateItem(index, 'quantity', value);\n                                                                            },\n                                                                            placeholder: \"1\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.quantity <= 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            required: true,\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            value: item.unitPrice || '',\n                                                                            onChange: (e)=>{\n                                                                                const value = parseFloat(e.target.value) || 0;\n                                                                                updateItem(index, 'unitPrice', value);\n                                                                            },\n                                                                            placeholder: \"0.00\",\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (item.totalPrice || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1 flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>removeItem(index),\n                                                                            disabled: items.length === 1,\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                lineNumber: 825,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 818,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id || \"item-\".concat(index), true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 13\n                                                }, this),\n                                                items.filter((item)=>item.description.trim() !== '').length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message warning\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon warning\",\n                                                                children: \"⚠️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text warning\",\n                                                                children: \"Please add at least one item to the invoice.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this),\n                                                items.some((item)=>item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message error\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-icon error\",\n                                                                children: \"❌\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-message-text error\",\n                                                                children: \"All items must have positive whole number quantities and non-negative unit prices.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section sample-style\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-header sample-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"modal-form-section-title sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"modal-form-section-icon sample-style orange\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Invoice Summary\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Subtotal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.subtotal.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Tax (\",\n                                                                    formData.taxRate,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 879,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-gray-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.taxAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-1\",\n                                                                children: \"Total Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-lg font-bold text-blue-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    formData.totalAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Payment Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Amount Paid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-green-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"Balance Due:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 901,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-sm font-semibold text-red-600\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            (formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0)).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 902,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"mt-2\",\n                                                                children: formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Paid in Full\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 25\n                                                                }, this) : ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-yellow-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Partially Paid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Unpaid\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (initialData === null || initialData === void 0 ? void 0 : initialData.payments) && initialData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs font-medium text-gray-500 mb-2\",\n                                                                children: \"Recent Payments\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"space-y-1\",\n                                                                children: [\n                                                                    initialData.payments.slice(0, 3).map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"flex justify-between items-center text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-gray-600\",\n                                                                                    children: new Date(payment.paymentDate).toLocaleDateString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 933,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"font-medium text-green-600\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        payment.amount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                                    lineNumber: 934,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                            lineNumber: 932,\n                                                                            columnNumber: 27\n                                                                        }, this)),\n                                                                    initialData.payments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-5bfaf54eaea5fa1d\" + \" \" + \"text-xs text-gray-500 text-center pt-1\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            initialData.payments.length - 3,\n                                                                            \" more payments\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                        lineNumber: 938,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            padding: '24px 24px 0 24px',\n                            borderTop: '1px solid #e2e8f0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            position: 'absolute',\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            minHeight: '60px',\n                            opacity: 1,\n                            transform: 'none',\n                            borderBottomLeftRadius: '12px',\n                            borderBottomRightRadius: '12px'\n                        },\n                        className: \"jsx-5bfaf54eaea5fa1d\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px',\n                                    color: '#64748b',\n                                    fontSize: '14px',\n                                    position: 'absolute',\n                                    left: '24px',\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        style: {\n                                            width: '16px',\n                                            height: '16px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 971,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '12px',\n                                    position: 'absolute',\n                                    left: '50%',\n                                    top: '50%',\n                                    transform: 'translate(-50%, -50%)',\n                                    zIndex: 11\n                                },\n                                className: \"jsx-5bfaf54eaea5fa1d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleDownloadPDF,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none',\n                                            marginRight: '12px',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '4px'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                style: {\n                                                    width: '12px',\n                                                    height: '12px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View / Print\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#6b7280',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                            transform: 'none'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#4b5563';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.backgroundColor = '#6b7280';\n                                            e.currentTarget.style.transform = 'none';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        onClick: handleSubmit,\n                                        disabled: loading,\n                                        style: {\n                                            padding: '12px 24px',\n                                            backgroundColor: '#3b82f6',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            cursor: loading ? 'not-allowed' : 'pointer',\n                                            fontSize: '14px',\n                                            fontWeight: '600',\n                                            transition: 'all 0.2s ease',\n                                            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                                            transform: 'none',\n                                            opacity: loading ? 0.6 : 1\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#2563eb';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!loading) {\n                                                e.currentTarget.style.backgroundColor = '#3b82f6';\n                                                e.currentTarget.style.transform = 'none';\n                                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';\n                                            }\n                                        },\n                                        className: \"jsx-5bfaf54eaea5fa1d\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            className: \"jsx-5bfaf54eaea5fa1d\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '12px',\n                                                        height: '12px',\n                                                        border: '2px solid transparent',\n                                                        borderTopColor: 'currentColor',\n                                                        borderRadius: '50%',\n                                                        animation: 'spin 1s linear infinite'\n                                                    },\n                                                    className: \"jsx-5bfaf54eaea5fa1d\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-5bfaf54eaea5fa1d\",\n                                                    children: \"Processing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 17\n                                        }, this) : initialData ? 'Update Invoice' : 'Create Invoice'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-form-modal.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"aYAldPW7wrxZCwRKgHEEWe0d8i8=\");\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/clients/invoice-form-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/main.css":
/*!*****************************!*\
  !*** ./src/styles/main.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c2054595b79b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvbWFpbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvc3R5bGVzL21haW4uY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzIwNTQ1OTViNzliXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/main.css\n"));

/***/ })

});