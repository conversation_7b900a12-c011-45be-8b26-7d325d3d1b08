/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/clients/route";
exports.ids = ["app/api/admin/clients/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_clients_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/clients/route.ts */ \"(rsc)/./src/app/api/admin/clients/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/clients/route\",\n        pathname: \"/api/admin/clients\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/clients/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/clients/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_clients_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/clients/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/clients/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/admin/clients/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _config_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prisma */ \"(rsc)/./src/config/prisma.ts\");\n/* harmony import */ var _services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api/api-utils */ \"(rsc)/./src/services/api/api-utils.ts\");\n/* harmony import */ var _lib_utils_validations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/validations */ \"(rsc)/./src/lib/utils/validations.ts\");\n/* harmony import */ var _lib_utils_data_transform__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/data-transform */ \"(rsc)/./src/lib/utils/data-transform.ts\");\n\n\n\n\n\n// GET /api/admin/clients - List all clients with pagination and search\nconst GET = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.withErrorHandler)(async (request)=>{\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n    const { page, limit, search, sortBy, sortOrder, filter } = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.getQueryParams)(request);\n    const { skip, take } = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.getPaginationParams)(page, limit);\n    // Build where clause\n    const where = {};\n    // Add search functionality\n    if (search && search.trim()) {\n        const searchQuery = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.buildSearchQuery)(search.trim(), [\n            'companyname',\n            'contactname',\n            'contactemail',\n            'city',\n            'state',\n            'country'\n        ]);\n        Object.assign(where, searchQuery);\n    }\n    // Add filters\n    if (filter) {\n        try {\n            const filters = JSON.parse(filter);\n            if (filters.isActive !== undefined) where.isactive = filters.isActive === 'true';\n        } catch (e) {\n        // Invalid filter JSON, ignore\n        }\n    }\n    // Build orderBy clause with field mapping\n    const fieldMapping = {\n        'createdAt': 'createdat',\n        'updatedAt': 'updatedat',\n        'companyName': 'companyname',\n        'contactName': 'contactname',\n        'contactEmail': 'contactemail',\n        'contactPhone': 'contactphone',\n        'contactFax': 'contactfax',\n        'companyWebsite': 'companywebsite',\n        'zipCode': 'zipcode',\n        'logoUrl': 'logourl',\n        'isActive': 'isactive'\n    };\n    const orderBy = {};\n    if (sortBy) {\n        const mappedSortBy = fieldMapping[sortBy] || sortBy;\n        orderBy[mappedSortBy] = sortOrder || 'asc';\n    } else {\n        orderBy.updatedat = 'desc'; // Default sort by Last Active\n    }\n    const [clients, total] = await Promise.all([\n        _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.findMany({\n            where,\n            skip,\n            take,\n            orderBy,\n            include: {\n                projects: {\n                    select: {\n                        id: true,\n                        name: true,\n                        status: true,\n                        projstartdate: true,\n                        projcompletiondate: true,\n                        estimatecost: true\n                    },\n                    orderBy: {\n                        createdat: 'desc'\n                    },\n                    take: 3\n                },\n                orders: {\n                    select: {\n                        id: true,\n                        ordertitle: true,\n                        ordertotalamount: true,\n                        status: true,\n                        orderdate: true\n                    },\n                    orderBy: {\n                        createdat: 'desc'\n                    },\n                    take: 3\n                },\n                users: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstname: true,\n                        lastname: true\n                    }\n                },\n                _count: {\n                    select: {\n                        projects: true,\n                        orders: true,\n                        invoices: true,\n                        contracts: true\n                    }\n                }\n            }\n        }),\n        _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.count({\n            where\n        })\n    ]);\n    // Transform the data for frontend\n    const transformedClients = clients.map((client)=>_lib_utils_data_transform__WEBPACK_IMPORTED_MODULE_4__.transformFromDbFields.client(client));\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.paginatedResponse)(transformedClients, page, limit, total);\n});\n// POST /api/admin/clients - Create a new client\nconst POST = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.withErrorHandler)(async (request)=>{\n    console.log('API - Starting client creation process');\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n    console.log('API - Admin authentication passed');\n    const validate = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.validateRequest)(_lib_utils_validations__WEBPACK_IMPORTED_MODULE_3__.schemas.client.create);\n    const data = await validate(request);\n    console.log('API - Client creation data received:', data);\n    console.log('API - Validation passed, proceeding with database operations');\n    // Check if a client with the same email already exists\n    console.log('API - Checking for existing client with email:', data.contactEmail);\n    const existingClient = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.findFirst({\n        where: {\n            contactemail: data.contactEmail\n        }\n    });\n    if (existingClient) {\n        console.log('API - Client with email already exists:', existingClient.id);\n        throw new Error('A client with this email already exists');\n    }\n    console.log('API - No existing client found, proceeding');\n    // If userId is provided, check if the user exists\n    if (data.userId) {\n        console.log('API - Checking if user exists:', data.userId);\n        const user = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.users.findUnique({\n            where: {\n                id: Number(data.userId)\n            }\n        });\n        if (!user) {\n            console.log('API - User not found:', data.userId);\n            throw new Error('User not found');\n        }\n        console.log('API - User found:', user.id);\n    }\n    console.log('API - Creating client in database');\n    const transformedData = _lib_utils_data_transform__WEBPACK_IMPORTED_MODULE_4__.transformToDbFields.client(data);\n    console.log('API - Transformed data for database:', transformedData);\n    const client = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.create({\n        data: transformedData,\n        include: {\n            _count: {\n                select: {\n                    projects: true,\n                    orders: true,\n                    invoices: true,\n                    contracts: true\n                }\n            }\n        }\n    });\n    console.log('API - Client created successfully:', client.id);\n    const transformedClient = _lib_utils_data_transform__WEBPACK_IMPORTED_MODULE_4__.transformFromDbFields.client(client);\n    console.log('API - Returning success response');\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.successResponse)(transformedClient, 'Client created successfully', 201);\n});\n// PUT /api/admin/clients - Bulk update clients\nconst PUT = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.withErrorHandler)(async (request)=>{\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n    const body = await request.json();\n    const { ids, data } = body;\n    if (!Array.isArray(ids) || ids.length === 0) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Invalid client IDs provided'\n        }, {\n            status: 400\n        });\n    }\n    const updatedClients = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.updateMany({\n        where: {\n            id: {\n                in: ids.map(Number)\n            }\n        },\n        data: _lib_utils_data_transform__WEBPACK_IMPORTED_MODULE_4__.transformToDbFields.client(data)\n    });\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.successResponse)({\n        count: updatedClients.count\n    }, `${updatedClients.count} clients updated successfully`);\n});\n// DELETE /api/admin/clients - Bulk delete clients\nconst DELETE = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.withErrorHandler)(async (request)=>{\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n    const body = await request.json();\n    const { ids } = body;\n    if (!Array.isArray(ids) || ids.length === 0) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Invalid client IDs provided'\n        }, {\n            status: 400\n        });\n    }\n    // Check if any clients have associated data that should be preserved\n    const clientsWithData = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.findMany({\n        where: {\n            id: {\n                in: ids.map(Number)\n            },\n            OR: [\n                {\n                    projects: {\n                        some: {}\n                    }\n                },\n                {\n                    orders: {\n                        some: {}\n                    }\n                },\n                {\n                    invoices: {\n                        some: {}\n                    }\n                },\n                {\n                    contracts: {\n                        some: {}\n                    }\n                }\n            ]\n        },\n        select: {\n            id: true,\n            companyname: true\n        }\n    });\n    if (clientsWithData.length > 0) {\n        const clientNames = clientsWithData.map((c)=>c.companyname).join(', ');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `Cannot delete clients with associated data: ${clientNames}. Please handle their projects, orders, invoices, and contracts first.`\n        }, {\n            status: 400\n        });\n    }\n    const deletedClients = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.clients.deleteMany({\n        where: {\n            id: {\n                in: ids.map(Number)\n            }\n        }\n    });\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_2__.successResponse)({\n        count: deletedClients.count\n    }, `${deletedClients.count} clients deleted successfully`);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/clients/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prisma.ts":
/*!******************************!*\
  !*** ./src/config/prisma.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ prisma),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// Re-export for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDRCQUE0QjtBQUNMIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvY29uZmlnL3ByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG5cbi8vIFJlLWV4cG9ydCBmb3IgY29udmVuaWVuY2VcbmV4cG9ydCB7IHByaXNtYSBhcyBkYiB9XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/data-transform.ts":
/*!*****************************************!*\
  !*** ./src/lib/utils/data-transform.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertIds: () => (/* binding */ convertIds),\n/* harmony export */   transformFromDbFields: () => (/* binding */ transformFromDbFields),\n/* harmony export */   transformToDbFields: () => (/* binding */ transformToDbFields)\n/* harmony export */ });\n// Data transformation utilities to handle field name mismatches between validation schemas and database schema\n// Transform camelCase validation data to database field names\nconst transformToDbFields = {\n    category: (data)=>({\n            categname: data.name,\n            categdesc: data.description,\n            parentid: data.parentId ? Number(data.parentId) : 0,\n            isactive: data.isActive ?? true,\n            displayorder: data.displayOrder ?? 0\n        }),\n    service: (data)=>({\n            categid: Number(data.categoryId),\n            name: data.name,\n            description: data.description,\n            iconclass: data.iconClass || 'fas fa-cog',\n            price: data.price ? Number(data.price) : 0,\n            discountrate: data.discountRate ?? 0,\n            manager: data.manager || 'Admin',\n            isactive: data.isActive ?? true,\n            displayorder: data.displayOrder ?? 0\n        }),\n    teamMember: (data)=>({\n            name: data.name,\n            position: data.position,\n            birthdate: data.birthDate ? new Date(data.birthDate) : null,\n            gender: data.gender,\n            maritalstatus: data.maritalStatus,\n            socialsecurityno: data.socialSecurityNo,\n            hiredate: data.hireDate ? new Date(data.hireDate) : null,\n            address: data.address,\n            city: data.city,\n            state: data.state,\n            zipcode: data.zipCode,\n            country: data.country,\n            phone: data.phone,\n            salary: data.salary ? Number(data.salary) : null,\n            payrollmethod: data.payrollMethod,\n            empresumeurl: data.resumeUrl,\n            notes: data.notes,\n            bio: data.bio,\n            photourl: data.photoUrl,\n            email: data.email,\n            linkedinurl: data.linkedinUrl,\n            twitterurl: data.twitterUrl,\n            githuburl: data.githubUrl,\n            displayorder: data.displayOrder ?? 0,\n            isactive: data.isActive ?? true\n        }),\n    blogPost: (data)=>({\n            authorid: data.authorId,\n            title: data.title,\n            content: data.content,\n            slug: data.slug,\n            featuredimageurl: data.featuredImageUrl,\n            excerpt: data.excerpt,\n            ispublished: data.isPublished ?? false,\n            publishedat: data.publishedAt ? new Date(data.publishedAt) : null,\n            categories: data.categories,\n            tags: data.tags\n        }),\n    contactForm: (data)=>({\n            name: data.name,\n            email: data.email,\n            phone: data.phone,\n            subject: data.subject,\n            message: data.message,\n            isread: data.isRead ?? false,\n            readat: data.readAt ? new Date(data.readAt) : null,\n            status: data.status || 'New'\n        }),\n    jobListing: (data)=>({\n            title: data.title,\n            description: data.description,\n            requirements: data.requirements,\n            location: data.location,\n            employmenttype: data.employmentType,\n            salarymin: data.salaryMin ? Number(data.salaryMin) : null,\n            salarymax: data.salaryMax ? Number(data.salaryMax) : null,\n            salarycurrency: data.salaryCurrency || 'USD',\n            isremote: data.isRemote ?? false,\n            isactive: data.isActive ?? true,\n            expiresat: data.expiresAt ? new Date(data.expiresAt) : null\n        }),\n    heroSection: (data)=>({\n            title: data.title,\n            metadescription: data.metaDescription,\n            metakeywords: data.metaKeywords,\n            pagename: data.pageName,\n            maintitle: data.mainTitle,\n            mainsubtitle: data.mainSubtitle,\n            maindescription: data.mainDescription,\n            primarybuttontext: data.primaryButtonText,\n            primarybuttonurl: data.primaryButtonUrl,\n            secondarybuttontext: data.secondaryButtonText,\n            secondarybuttonurl: data.secondaryButtonUrl,\n            enableslideshow: data.enableSlideshow ? 1 : 0,\n            slideshowspeed: data.slideshowSpeed ?? 5000,\n            autoplay: data.autoplay ? 1 : 0,\n            showdots: data.showDots ? 1 : 0,\n            showarrows: data.showArrows ? 1 : 0,\n            enablefloatingelements: data.enableFloatingElements ? 1 : 0,\n            floatingelementsconfig: data.floatingElementsConfig,\n            isactive: data.isActive ?? true,\n            modifiedby: data.modifiedBy\n        }),\n    heroSlide: (data)=>({\n            herosectionid: Number(data.heroSectionId),\n            content: data.content,\n            mediatype: data.mediaType ?? 'image',\n            imageurl: data.imageUrl,\n            videourl: data.videoUrl,\n            mediaalt: data.mediaAlt,\n            videoautoplay: data.videoAutoplay ? 1 : 0,\n            videomuted: data.videoMuted ? 1 : 0,\n            videoloop: data.videoLoop ? 1 : 0,\n            videocontrols: data.videoControls ? 1 : 0,\n            buttontext: data.buttonText,\n            buttonurl: data.buttonUrl,\n            displayorder: data.displayOrder ?? 0,\n            isactive: data.isActive ?? true,\n            animationtype: data.animationType ?? 'fade',\n            duration: data.duration ?? 5000\n        }),\n    aboutPage: (data)=>({\n            title: data.title,\n            subtitle: data.subtitle,\n            content: data.content,\n            missiontitle: data.missionTitle,\n            missioncontent: data.missionContent,\n            visiontitle: data.visionTitle,\n            visioncontent: data.visionContent,\n            valuestitle: data.valuesTitle,\n            valuescontent: data.valuesContent,\n            ctatitle: data.ctaTitle,\n            ctasubtitle: data.ctaSubtitle,\n            ctaprimarybuttontext: data.ctaPrimaryButtonText,\n            ctaprimarybuttonurl: data.ctaPrimaryButtonUrl,\n            ctasecondarybuttontext: data.ctaSecondaryButtonText,\n            ctasecondarybuttonurl: data.ctaSecondaryButtonUrl,\n            isactive: data.isActive ?? true,\n            modifiedby: data.modifiedBy\n        }),\n    legalPage: (data)=>({\n            title: data.title,\n            slug: data.slug,\n            metadescription: data.metaDescription,\n            content: data.content,\n            displayorder: data.displayOrder ?? 0,\n            isactive: data.isActive ?? true,\n            lastmodified: data.lastModified ? new Date(data.lastModified) : new Date(),\n            modifiedby: data.modifiedBy || 'admin'\n        }),\n    siteSetting: (data)=>({\n            settingkey: data.settingKey,\n            settingvalue: data.settingValue,\n            settingtype: data.settingType || 'text',\n            description: data.description,\n            isactive: data.isActive ?? true\n        }),\n    client: (data)=>({\n            userid: data.userId ? Number(data.userId) : null,\n            companyname: data.companyName,\n            contactname: data.contactName,\n            contactposition: data.contactPosition,\n            contactemail: data.contactEmail,\n            contactphone: data.contactPhone,\n            contactfax: data.contactFax,\n            companywebsite: data.website || data.companyWebsite,\n            address: data.address,\n            city: data.city,\n            state: data.state,\n            zipcode: data.zipCode,\n            country: data.country,\n            logourl: data.logoUrl,\n            notes: data.notes,\n            isactive: data.isActive ?? true\n        }),\n    project: (data)=>({\n            name: data.name,\n            description: data.description,\n            projgoals: data.goals,\n            projmanager: data.managerId ? Number(data.managerId) : null,\n            clientid: data.clientId ? Number(data.clientId) : null,\n            orderid: data.orderId ? Number(data.orderId) : null,\n            imageurl: data.imageUrl,\n            projecturl: data.projectUrl,\n            githuburl: data.githubUrl,\n            tags: data.tags,\n            projstartdate: data.startDate ? new Date(data.startDate) : null,\n            projcompletiondate: data.completionDate ? new Date(data.completionDate) : null,\n            estimatecost: data.estimatedCost ? Number(data.estimatedCost) : null,\n            estimatetime: data.estimatedTime,\n            estimateeffort: data.estimatedEffort,\n            status: data.status,\n            isfeatured: data.isFeatured ?? false,\n            ispublic: data.isPublic ?? true,\n            displayorder: data.displayOrder ?? 0\n        }),\n    user: (data)=>({\n            email: data.email,\n            password: data.password,\n            firstname: data.firstname,\n            lastname: data.lastname,\n            role: data.role,\n            imageurl: data.imageurl,\n            isactive: data.isactive ?? true,\n            emailverified: data.emailVerified ? new Date(data.emailVerified) : null\n        }),\n    testimonial: (data)=>({\n            clientid: Number(data.clientId),\n            clientname: data.clientName,\n            clienttitle: data.clientTitle,\n            clientcompany: data.clientCompany,\n            clientphotourl: data.clientPhotoUrl,\n            content: data.content,\n            rating: Number(data.rating),\n            isfeatured: data.isFeatured ?? false,\n            displayorder: data.displayOrder ?? 0\n        }),\n    order: (data)=>({\n            ordertitle: data.title,\n            clientid: Number(data.clientId),\n            ordermanager: data.managerId ? Number(data.managerId) : null,\n            orderdesc: data.description,\n            orderdate: data.orderDate ? new Date(data.orderDate) : new Date(),\n            ordertotalamount: data.totalAmount ? Number(data.totalAmount) : 0,\n            orderdiscountrate: data.discountRate ?? 0,\n            status: data.status,\n            notes: data.notes,\n            isactive: data.isActive ?? true\n        }),\n    invoice: (data)=>({\n            duedate: new Date(data.dueDate),\n            subtotal: data.subtotal ? Number(data.subtotal) : 0,\n            taxrate: data.taxRate ? Number(data.taxRate) : 0,\n            taxamount: data.taxAmount ? Number(data.taxAmount) : 0,\n            totalamount: data.totalAmount ? Number(data.totalAmount) : 0,\n            status: data.status,\n            description: data.description,\n            clientid: Number(data.clientId),\n            contid: data.contractId ? Number(data.contractId) : null,\n            orderid: data.orderId ? Number(data.orderId) : null,\n            projectid: data.projectId ? Number(data.projectId) : null,\n            paidat: data.paidAt ? new Date(data.paidAt) : null\n        })\n};\n// Transform database fields back to camelCase for API responses\nconst transformFromDbFields = {\n    category: (data)=>({\n            id: data.id,\n            name: data.categname,\n            description: data.categdesc,\n            parentId: data.parentid,\n            isActive: data.isactive,\n            displayOrder: data.displayorder,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat\n        }),\n    service: (data)=>({\n            id: Number(data.id),\n            categoryId: Number(data.categid),\n            name: data.name,\n            description: data.description,\n            iconClass: data.iconclass,\n            price: data.price,\n            discountRate: data.discountrate,\n            manager: data.manager,\n            isActive: data.isactive,\n            displayOrder: data.displayorder,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            category: data.categories ? {\n                id: Number(data.categories.id),\n                name: data.categories.categname\n            } : null,\n            serviceOptions: data.serviceoptions ? data.serviceoptions.map((opt)=>({\n                    id: Number(opt.id),\n                    name: opt.optname,\n                    price: opt.optprice\n                })) : [],\n            _count: data._count ? {\n                orderDetails: data._count.orderdetails || 0,\n                serviceOptions: data._count.serviceoptions || 0\n            } : null\n        }),\n    client: (data)=>({\n            id: Number(data.id),\n            userId: data.userid ? Number(data.userid) : null,\n            companyName: data.companyname,\n            contactName: data.contactname,\n            contactPosition: data.contactposition,\n            contactEmail: data.contactemail,\n            contactPhone: data.contactphone,\n            contactFax: data.contactfax,\n            website: data.companywebsite,\n            companyWebsite: data.companywebsite,\n            address: data.address,\n            city: data.city,\n            state: data.state,\n            zipCode: data.zipcode,\n            country: data.country,\n            logoUrl: data.logourl,\n            isActive: data.isactive,\n            notes: data.notes,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            _count: data._count ? {\n                projects: data._count.projects || 0,\n                orders: data._count.orders || 0,\n                testimonials: data._count.testimonials || 0\n            } : null\n        }),\n    project: (data)=>({\n            id: Number(data.id),\n            name: data.name,\n            description: data.description,\n            goals: data.projgoals,\n            managerId: data.projmanager ? Number(data.projmanager) : null,\n            clientId: data.clientid ? Number(data.clientid) : null,\n            orderId: data.orderid ? Number(data.orderid) : null,\n            imageUrl: data.imageurl,\n            projectUrl: data.projecturl,\n            githubUrl: data.githuburl,\n            tags: data.tags,\n            startDate: data.projstartdate,\n            completionDate: data.projcompletiondate,\n            estimateCost: data.estimatecost,\n            estimateTime: data.estimatetime,\n            estimateEffort: data.estimateeffort,\n            status: data.status,\n            isFeatured: data.isfeatured,\n            isPublic: data.ispublic,\n            displayOrder: data.displayorder,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            client: data.clients ? {\n                id: Number(data.clients.id),\n                companyName: data.clients.companyname\n            } : null,\n            feedbacks: data.feedbacks || []\n        }),\n    teamMember: (data)=>({\n            id: Number(data.id),\n            name: data.name,\n            position: data.position,\n            birthDate: data.birthdate,\n            gender: data.gender,\n            maritalStatus: data.maritalstatus,\n            socialSecurityNo: data.socialsecurityno,\n            hireDate: data.hiredate,\n            address: data.address,\n            city: data.city,\n            state: data.state,\n            zipCode: data.zipcode,\n            country: data.country,\n            phone: data.phone,\n            salary: data.salary,\n            payrollMethod: data.payrollmethod,\n            resumeUrl: data.empresumeurl,\n            notes: data.notes,\n            bio: data.bio,\n            photoUrl: data.photourl,\n            email: data.email,\n            linkedinUrl: data.linkedinurl,\n            twitterUrl: data.twitterurl,\n            githubUrl: data.githuburl,\n            displayOrder: data.displayorder,\n            isActive: data.isactive,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            projects: data.projects ? data.projects.map((project)=>({\n                    id: Number(project.id),\n                    name: project.name,\n                    status: project.status\n                })) : [],\n            payrollRecords: data.payrollrecords ? data.payrollrecords.map((record)=>({\n                    id: Number(record.id),\n                    payDate: record.paydate,\n                    grossPay: record.grosspay\n                })) : [],\n            tasks: data.tasks ? data.tasks.map((task)=>({\n                    id: Number(task.id),\n                    taskDesc: task.taskdesc,\n                    status: task.status\n                })) : [],\n            _count: data._count ? {\n                projects: data._count.projects || 0,\n                payrollRecords: data._count.payrollrecords || 0,\n                tasks: data._count.tasks || 0\n            } : null\n        }),\n    technology: (data)=>({\n            id: Number(data.id),\n            name: data.name,\n            description: data.description,\n            iconUrl: data.iconurl,\n            displayOrder: data.displayorder,\n            isActive: data.isactive,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            _count: data._count ? {\n                projectTechnologies: data._count.projecttechnologies || 0\n            } : null\n        }),\n    testimonial: (data)=>({\n            id: Number(data.id),\n            clientName: data.clientname,\n            clientTitle: data.clienttitle,\n            clientCompany: data.clientcompany,\n            clientPhotoUrl: data.clientphotourl,\n            content: data.content,\n            rating: data.rating,\n            projectId: data.projectid ? Number(data.projectid) : null,\n            clientId: data.clientid ? Number(data.clientid) : null,\n            isPublic: data.ispublic,\n            isFeatured: data.isfeatured,\n            displayOrder: data.displayorder,\n            isActive: data.isactive,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat\n        }),\n    contactForm: (data)=>({\n            id: typeof data.id === 'bigint' ? data.id.toString() : data.id,\n            name: data.name,\n            email: data.email,\n            phone: data.phone,\n            subject: data.subject,\n            message: data.message,\n            isRead: data.isread,\n            readAt: data.readat,\n            status: data.status,\n            parentId: typeof data.parentid === 'bigint' ? data.parentid.toString() : data.parentid,\n            threadId: typeof data.threadid === 'bigint' ? data.threadid.toString() : data.threadid,\n            senderId: typeof data.senderid === 'bigint' ? data.senderid.toString() : data.senderid,\n            receiverId: typeof data.receiverid === 'bigint' ? data.receiverid.toString() : data.receiverid,\n            messageType: data.messagetype,\n            contentType: data.contenttype,\n            attachments: data.attachments,\n            isDelivered: data.isdelivered,\n            deliveredAt: data.deliveredat,\n            editHistory: data.edithistory,\n            metadata: data.metadata,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat\n        }),\n    blogPost: (data)=>({\n            id: Number(data.id),\n            authorId: data.authorid ? Number(data.authorid) : null,\n            title: data.title,\n            content: data.content,\n            slug: data.slug,\n            featuredImageUrl: data.featuredimageurl,\n            excerpt: data.excerpt,\n            isPublished: data.ispublished,\n            publishedAt: data.publishedat,\n            categories: data.categories,\n            tags: data.tags,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            author: data.author ? {\n                id: Number(data.author.id),\n                firstName: data.author.firstname,\n                lastName: data.author.lastname,\n                email: data.author.email\n            } : null\n        }),\n    heroSection: (data)=>({\n            id: Number(data.id),\n            title: data.title,\n            metaDescription: data.metadescription,\n            metaKeywords: data.metakeywords,\n            pageName: data.pagename,\n            mainTitle: data.maintitle,\n            mainSubtitle: data.mainsubtitle,\n            mainDescription: data.maindescription,\n            primaryButtonText: data.primarybuttontext,\n            primaryButtonUrl: data.primarybuttonurl,\n            secondaryButtonText: data.secondarybuttontext,\n            secondaryButtonUrl: data.secondarybuttonurl,\n            enableSlideshow: Boolean(data.enableslideshow),\n            slideshowSpeed: data.slideshowspeed,\n            autoplay: Boolean(data.autoplay),\n            showDots: Boolean(data.showdots),\n            showArrows: Boolean(data.showarrows),\n            enableFloatingElements: Boolean(data.enablefloatingelements),\n            floatingElementsConfig: data.floatingelementsconfig,\n            isActive: data.isactive,\n            modifiedBy: data.modifiedby,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            slides: data.heroslides ? data.heroslides.map((slide)=>transformFromDbFields.heroSlide(slide)) : []\n        }),\n    heroSlide: (data)=>({\n            id: Number(data.id),\n            heroSectionId: Number(data.herosectionid),\n            content: data.content,\n            mediaType: data.mediatype,\n            imageUrl: data.imageurl,\n            videoUrl: data.videourl,\n            mediaAlt: data.mediaalt,\n            videoAutoplay: Boolean(data.videoautoplay),\n            videoMuted: Boolean(data.videomuted),\n            videoLoop: Boolean(data.videoloop),\n            videoControls: Boolean(data.videocontrols),\n            buttonText: data.buttontext,\n            buttonUrl: data.buttonurl,\n            displayOrder: data.displayorder,\n            isActive: data.isactive,\n            animationType: data.animationtype,\n            duration: data.duration,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat\n        }),\n    user: (data)=>({\n            id: Number(data.id),\n            email: data.email,\n            emailVerified: data.emailverified,\n            firstName: data.firstname,\n            lastName: data.lastname,\n            imageUrl: data.imageurl,\n            role: data.role,\n            isActive: data.isactive,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat\n        }),\n    legalPage: (data)=>({\n            id: Number(data.id),\n            title: data.title,\n            slug: data.slug,\n            metaDescription: data.metadescription,\n            content: data.content,\n            isActive: data.isactive,\n            displayOrder: data.displayorder,\n            lastModified: data.lastmodified,\n            modifiedBy: data.modifiedby,\n            createdAt: data.createdat,\n            updatedAt: data.updatedat,\n            sections: data.legalpagesections ? data.legalpagesections.map((section)=>({\n                    id: Number(section.id),\n                    legalPageId: Number(section.legalpageid),\n                    title: section.title,\n                    content: section.content,\n                    iconClass: section.iconclass,\n                    displayOrder: section.displayorder,\n                    isActive: section.isactive,\n                    createdAt: section.createdat,\n                    updatedAt: section.updatedat\n                })) : []\n        })\n};\n// Helper function to convert string IDs to numbers for BigInt fields\nconst convertIds = (data, idFields)=>{\n    const converted = {\n        ...data\n    };\n    idFields.forEach((field)=>{\n        if (converted[field] && typeof converted[field] === 'string') {\n            converted[field] = Number(converted[field]);\n        }\n    });\n    return converted;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/data-transform.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/validations.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/validations.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAboutPageSchema: () => (/* binding */ createAboutPageSchema),\n/* harmony export */   createAboutPageSectionSchema: () => (/* binding */ createAboutPageSectionSchema),\n/* harmony export */   createBlogPostSchema: () => (/* binding */ createBlogPostSchema),\n/* harmony export */   createCategorySchema: () => (/* binding */ createCategorySchema),\n/* harmony export */   createClientSchema: () => (/* binding */ createClientSchema),\n/* harmony export */   createContactFormSchema: () => (/* binding */ createContactFormSchema),\n/* harmony export */   createContractSchema: () => (/* binding */ createContractSchema),\n/* harmony export */   createHeroSectionSchema: () => (/* binding */ createHeroSectionSchema),\n/* harmony export */   createHeroSlideSchema: () => (/* binding */ createHeroSlideSchema),\n/* harmony export */   createInvoiceSchema: () => (/* binding */ createInvoiceSchema),\n/* harmony export */   createJobApplicationSchema: () => (/* binding */ createJobApplicationSchema),\n/* harmony export */   createJobListingSchema: () => (/* binding */ createJobListingSchema),\n/* harmony export */   createLegalPageSchema: () => (/* binding */ createLegalPageSchema),\n/* harmony export */   createLegalPageSectionSchema: () => (/* binding */ createLegalPageSectionSchema),\n/* harmony export */   createOrderSchema: () => (/* binding */ createOrderSchema),\n/* harmony export */   createProjectSchema: () => (/* binding */ createProjectSchema),\n/* harmony export */   createServiceOptionFeatureSchema: () => (/* binding */ createServiceOptionFeatureSchema),\n/* harmony export */   createServiceOptionSchema: () => (/* binding */ createServiceOptionSchema),\n/* harmony export */   createServiceSchema: () => (/* binding */ createServiceSchema),\n/* harmony export */   createSiteSettingSchema: () => (/* binding */ createSiteSettingSchema),\n/* harmony export */   createTeamMemberSchema: () => (/* binding */ createTeamMemberSchema),\n/* harmony export */   createTechnologySchema: () => (/* binding */ createTechnologySchema),\n/* harmony export */   createTestimonialSchema: () => (/* binding */ createTestimonialSchema),\n/* harmony export */   createUserSchema: () => (/* binding */ createUserSchema),\n/* harmony export */   schemas: () => (/* binding */ schemas),\n/* harmony export */   updateAboutPageSchema: () => (/* binding */ updateAboutPageSchema),\n/* harmony export */   updateAboutPageSectionSchema: () => (/* binding */ updateAboutPageSectionSchema),\n/* harmony export */   updateBlogPostSchema: () => (/* binding */ updateBlogPostSchema),\n/* harmony export */   updateCategorySchema: () => (/* binding */ updateCategorySchema),\n/* harmony export */   updateClientSchema: () => (/* binding */ updateClientSchema),\n/* harmony export */   updateContactFormSchema: () => (/* binding */ updateContactFormSchema),\n/* harmony export */   updateContractSchema: () => (/* binding */ updateContractSchema),\n/* harmony export */   updateHeroSectionSchema: () => (/* binding */ updateHeroSectionSchema),\n/* harmony export */   updateHeroSlideSchema: () => (/* binding */ updateHeroSlideSchema),\n/* harmony export */   updateInvoiceSchema: () => (/* binding */ updateInvoiceSchema),\n/* harmony export */   updateJobApplicationSchema: () => (/* binding */ updateJobApplicationSchema),\n/* harmony export */   updateJobListingSchema: () => (/* binding */ updateJobListingSchema),\n/* harmony export */   updateLegalPageSchema: () => (/* binding */ updateLegalPageSchema),\n/* harmony export */   updateLegalPageSectionSchema: () => (/* binding */ updateLegalPageSectionSchema),\n/* harmony export */   updateOrderSchema: () => (/* binding */ updateOrderSchema),\n/* harmony export */   updateProjectSchema: () => (/* binding */ updateProjectSchema),\n/* harmony export */   updateServiceOptionFeatureSchema: () => (/* binding */ updateServiceOptionFeatureSchema),\n/* harmony export */   updateServiceOptionSchema: () => (/* binding */ updateServiceOptionSchema),\n/* harmony export */   updateServiceSchema: () => (/* binding */ updateServiceSchema),\n/* harmony export */   updateSiteSettingSchema: () => (/* binding */ updateSiteSettingSchema),\n/* harmony export */   updateTeamMemberSchema: () => (/* binding */ updateTeamMemberSchema),\n/* harmony export */   updateTechnologySchema: () => (/* binding */ updateTechnologySchema),\n/* harmony export */   updateTestimonialSchema: () => (/* binding */ updateTestimonialSchema),\n/* harmony export */   updateUserSchema: () => (/* binding */ updateUserSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/coerce.js\");\n\n// User schemas\nconst createUserSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    firstname: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    lastname: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    imageurl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'ADMIN',\n        'USER',\n        'CLIENT'\n    ]).default('USER'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, 'Password must be at least 6 characters').optional(),\n    isactive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    linkedclientid: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>val ? Number(val) : undefined).optional()\n});\nconst updateUserSchema = createUserSchema.partial();\n// Service schemas\nconst createServiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    categoryId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(255).optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    excerpt: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500).optional(),\n    iconClass: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    logoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    price: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(0).max(100).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    manager: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    duration: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    complexity: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'Simple',\n        'Medium',\n        'Complex'\n    ]).optional(),\n    features: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateServiceSchema = createServiceSchema.partial();\n// Service Option schemas\nconst createServiceOptionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    serviceId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(50),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    price: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(0).max(100).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateServiceOptionSchema = createServiceOptionSchema.partial();\n// Service Option Feature schemas\nconst createServiceOptionFeatureSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    optionId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(50),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    cost: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(100).transform((val)=>Math.round(val)).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    isIncluded: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateServiceOptionFeatureSchema = createServiceOptionFeatureSchema.partial();\n// Project schemas\nconst createProjectSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    orderid: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    clientid: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Client is required\"),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    projgoals: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projmanager: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projstartdate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    projcompletiondate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    estimatecost: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    estimatetime: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    estimateeffort: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PLANNING',\n        'IN_PROGRESS',\n        'COMPLETED',\n        'ON_HOLD',\n        'CANCELLED'\n    ]).default('PLANNING'),\n    isfeatured: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    ispublic: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayorder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    imageurl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projecturl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    githuburl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    tags: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    })\n});\nconst updateProjectSchema = createProjectSchema.partial().extend({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Client is required\") // Keep clientid required for updates\n});\n// Client schemas\nconst createClientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    companyName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(200),\n    contactName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    contactPosition: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    contactEmail: zod__WEBPACK_IMPORTED_MODULE_0__.string().email().max(100),\n    contactPhone: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    contactFax: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    website: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    companyWebsite: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(200).optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    logoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500).optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateClientSchema = createClientSchema.partial();\n// Blog post schemas\nconst createBlogPostSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    excerpt: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    featuredImageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    authorId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    isPublished: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    publishedAt: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().nullable().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    categories: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    tags: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined)\n});\nconst updateBlogPostSchema = createBlogPostSchema.partial();\n// Team member schemas\nconst createTeamMemberSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    position: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    birthDate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    gender: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    maritalStatus: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    socialSecurityNo: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    hireDate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    salary: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).optional().nullable().transform((val)=>{\n        if (!val || val === '' || val === null) return undefined;\n        return typeof val === 'string' ? parseFloat(val) : val;\n    }),\n    payrollMethod: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    resumeUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    bio: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    photoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email().optional().nullable().transform((val)=>val || undefined),\n    linkedinUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    twitterUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    githubUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateTeamMemberSchema = createTeamMemberSchema.partial();\n// Technology schemas\nconst createTechnologySchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    iconUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateTechnologySchema = createTechnologySchema.partial();\n// Testimonial schemas\nconst createTestimonialSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientTitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientCompany: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientPhotoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    rating: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(5).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 5 : val;\n    }),\n    isFeatured: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    })\n});\nconst updateTestimonialSchema = createTestimonialSchema.partial();\n// Contact form schemas\nconst createContactFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isread: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    readat: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'New',\n        'In Progress',\n        'Resolved',\n        'Closed'\n    ]).default('New')\n});\nconst updateContactFormSchema = createContactFormSchema.partial();\n// Category schemas\nconst createCategorySchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    parentId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateCategorySchema = createCategorySchema.partial();\n// Order schemas\nconst createOrderSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    orderNumber: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    totalAmount: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PENDING',\n        'CONFIRMED',\n        'IN_PROGRESS',\n        'COMPLETED',\n        'CANCELLED'\n    ]).default('PENDING'),\n    orderDate: zod__WEBPACK_IMPORTED_MODULE_0__.date().default(()=>new Date())\n});\nconst updateOrderSchema = createOrderSchema.partial();\n// Invoice schemas\nconst createInvoiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    projectid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    orderid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    contid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')).transform((val)=>val === '' ? undefined : val),\n    subtotal: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxrate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).default(0),\n    taxamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).default(0),\n    totalamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'SENT',\n        'PAID',\n        'OVERDUE',\n        'CANCELLED',\n        'Pending',\n        'Sent',\n        'Paid',\n        'Overdue',\n        'Cancelled'\n    ]).default('DRAFT'),\n    duedate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    paidat: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional()\n});\nconst updateInvoiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    projectid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    orderid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    contid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.literal('')\n    ]).transform((val)=>val === '' || val === null ? undefined : val).optional(),\n    subtotal: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxrate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    totalamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'SENT',\n        'PAID',\n        'OVERDUE',\n        'CANCELLED',\n        'Pending',\n        'Sent',\n        'Paid',\n        'Overdue',\n        'Cancelled'\n    ]).optional(),\n    duedate: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    paidat: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.literal('')\n    ]).transform((val)=>val === '' || val === null ? undefined : val ? new Date(val) : undefined).optional()\n});\n// Job listing schemas\nconst createJobListingSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    requirements: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    location: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    employmentType: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    salaryMin: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    salaryMax: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    salaryCurrency: zod__WEBPACK_IMPORTED_MODULE_0__.string().default('USD'),\n    isRemote: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    expiresAt: zod__WEBPACK_IMPORTED_MODULE_0__.date().optional()\n});\nconst updateJobListingSchema = createJobListingSchema.partial();\n// Job Application schemas\nconst createJobApplicationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jobListingId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    applicantName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    applicantEmail: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    applicantPhone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    resumeUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    coverLetter: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PENDING',\n        'REVIEWED',\n        'INTERVIEW',\n        'HIRED',\n        'REJECTED'\n    ]).default('PENDING'),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateJobApplicationSchema = createJobApplicationSchema.partial();\n// Contract schemas\nconst createContractSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    projectId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    orderId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    contractNumber: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    value: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'PENDING',\n        'SIGNED',\n        'ACTIVE',\n        'COMPLETED',\n        'TERMINATED'\n    ]).default('DRAFT'),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    signedAt: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    expiresAt: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateContractSchema = createContractSchema.partial();\n// Additional schemas for missing models\n// Hero Section schemas\nconst createHeroSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    metaKeywords: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    pageName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).default('Home'),\n    mainTitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mainSubtitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mainDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    primaryButtonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    primaryButtonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    secondaryButtonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    secondaryButtonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    enableSlideshow: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    slideshowSpeed: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(1000).max(10000).default(5000),\n    autoplay: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    showDots: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    showArrows: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    enableFloatingElements: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    floatingElementsConfig: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    modifiedBy: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateHeroSectionSchema = createHeroSectionSchema.partial();\n// Hero Slide schemas\nconst createHeroSlideSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    heroSectionId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mediaType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'image',\n        'video'\n    ]).default('image'),\n    imageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    videoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    mediaAlt: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    videoAutoplay: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoMuted: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoLoop: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoControls: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    buttonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    buttonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    animationType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'fade',\n        'slide',\n        'zoom'\n    ]).default('fade'),\n    duration: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(1000).max(10000).default(5000)\n});\nconst updateHeroSlideSchema = createHeroSlideSchema.partial();\n// About Page schemas\nconst createAboutPageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateAboutPageSchema = createAboutPageSchema.partial();\n// About Page Section schemas\nconst createAboutPageSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    aboutPageId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    imageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateAboutPageSectionSchema = createAboutPageSectionSchema.partial();\n// Legal Page schemas\nconst createLegalPageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).optional(),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    modifiedBy: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || 'admin'),\n    lastModified: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return new Date();\n        return typeof val === 'string' ? new Date(val) : val;\n    })\n});\nconst updateLegalPageSchema = createLegalPageSchema.partial();\n// Legal Page Section schemas\nconst createLegalPageSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    legalPageId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateLegalPageSectionSchema = createLegalPageSectionSchema.partial();\n// Site Setting schemas\nconst createSiteSettingSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    key: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    value: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    category: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isactive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateSiteSettingSchema = createSiteSettingSchema.partial();\n// Export all schemas as a single object for easier imports\nconst schemas = {\n    user: {\n        create: createUserSchema,\n        update: updateUserSchema\n    },\n    service: {\n        create: createServiceSchema,\n        update: updateServiceSchema\n    },\n    project: {\n        create: createProjectSchema,\n        update: updateProjectSchema\n    },\n    client: {\n        create: createClientSchema,\n        update: updateClientSchema\n    },\n    blog: {\n        create: createBlogPostSchema,\n        update: updateBlogPostSchema\n    },\n    teamMember: {\n        create: createTeamMemberSchema,\n        update: updateTeamMemberSchema\n    },\n    technology: {\n        create: createTechnologySchema,\n        update: updateTechnologySchema\n    },\n    testimonial: {\n        create: createTestimonialSchema,\n        update: updateTestimonialSchema\n    },\n    contactForm: {\n        create: createContactFormSchema,\n        update: updateContactFormSchema\n    },\n    category: {\n        create: createCategorySchema,\n        update: updateCategorySchema\n    },\n    order: {\n        create: createOrderSchema,\n        update: updateOrderSchema\n    },\n    invoice: {\n        create: createInvoiceSchema,\n        update: updateInvoiceSchema\n    },\n    jobListing: {\n        create: createJobListingSchema,\n        update: updateJobListingSchema\n    },\n    jobApplication: {\n        create: createJobApplicationSchema,\n        update: updateJobApplicationSchema\n    },\n    contract: {\n        create: createContractSchema,\n        update: updateContractSchema\n    },\n    heroSection: {\n        create: createHeroSectionSchema,\n        update: updateHeroSectionSchema\n    },\n    heroSlide: {\n        create: createHeroSlideSchema,\n        update: updateHeroSlideSchema\n    },\n    aboutPage: {\n        create: createAboutPageSchema,\n        update: updateAboutPageSchema\n    },\n    aboutPageSection: {\n        create: createAboutPageSectionSchema,\n        update: updateAboutPageSectionSchema\n    },\n    legalPage: {\n        create: createLegalPageSchema,\n        update: updateLegalPageSchema\n    },\n    legalPageSection: {\n        create: createLegalPageSectionSchema,\n        update: updateLegalPageSectionSchema\n    },\n    siteSetting: {\n        create: createSiteSettingSchema,\n        update: updateSiteSettingSchema\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/validations.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/api/api-utils.ts":
/*!***************************************!*\
  !*** ./src/services/api/api-utils.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   buildSearchQuery: () => (/* binding */ buildSearchQuery),\n/* harmony export */   buildSortQuery: () => (/* binding */ buildSortQuery),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   validateMethod: () => (/* binding */ validateMethod),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest),\n/* harmony export */   withErrorHandler: () => (/* binding */ withErrorHandler)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/errors.js\");\n\n\n\n// Error handling\nclass ApiError extends Error {\n    constructor(message, statusCode = 500, code){\n        super(message), this.message = message, this.statusCode = statusCode, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Success response helper\nfunction successResponse(data, message, statusCode = 200) {\n    // Serialize BigInt values in the data\n    const serializedData = serializeBigInt(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        data: serializedData,\n        message\n    }, {\n        status: statusCode\n    });\n}\n// Error response helper\nfunction errorResponse(error, statusCode = 500, code) {\n    const message = error instanceof Error ? error.message : error;\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: false,\n        error: message,\n        code\n    }, {\n        status: statusCode\n    });\n}\n// Paginated response helper\n// Helper function to serialize BigInt and Decimal values\nfunction serializeBigInt(obj) {\n    if (obj === null || obj === undefined) return obj;\n    if (typeof obj === 'bigint') {\n        return obj.toString();\n    }\n    // Handle Date objects\n    if (obj instanceof Date) {\n        return obj.toISOString();\n    }\n    // Handle Prisma Decimal types\n    if (obj && typeof obj === 'object' && obj.constructor && obj.constructor.name === 'Decimal') {\n        return obj.toString();\n    }\n    if (Array.isArray(obj)) {\n        return obj.map(serializeBigInt);\n    }\n    if (typeof obj === 'object') {\n        const serialized = {};\n        for (const [key, value] of Object.entries(obj)){\n            serialized[key] = serializeBigInt(value);\n        }\n        return serialized;\n    }\n    return obj;\n}\nfunction paginatedResponse(data, page, limit, total, message) {\n    const totalPages = Math.ceil(total / limit);\n    // Serialize BigInt values in the data\n    const serializedData = serializeBigInt(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        data: serializedData,\n        message,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages\n        }\n    });\n}\n// Validation middleware\nfunction validateRequest(schema) {\n    return async (request)=>{\n        try {\n            const body = await request.json();\n            return schema.parse(body);\n        } catch (error) {\n            if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodError) {\n                const errorMessages = error.issues.map((err)=>`${err.path.join('.')}: ${err.message}`).join(', ');\n                throw new ApiError(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR');\n            }\n            throw new ApiError('Invalid request body', 400, 'INVALID_BODY');\n        }\n    };\n}\n// Query parameter helpers\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    return {\n        page: parseInt(searchParams.get('page') || '1'),\n        limit: Math.min(parseInt(searchParams.get('limit') || '10'), 100),\n        search: searchParams.get('search') || undefined,\n        sortBy: searchParams.get('sortBy') || undefined,\n        sortOrder: searchParams.get('sortOrder') || 'desc',\n        filter: searchParams.get('filter') || undefined,\n        categoryId: searchParams.get('categoryId') || undefined,\n        serviceId: searchParams.get('serviceId') || undefined,\n        optionId: searchParams.get('optionId') || undefined\n    };\n}\n// Pagination helpers\nfunction getPaginationParams(page, limit) {\n    const skip = (page - 1) * limit;\n    return {\n        skip,\n        take: limit\n    };\n}\n// Error handler wrapper for API routes\nfunction withErrorHandler(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            // Log errors only in development mode\n            if (true) {\n                console.error('API Error:', error);\n            }\n            if (error instanceof ApiError) {\n                return errorResponse(error.message, error.statusCode, error.code);\n            }\n            if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodError) {\n                const errorMessages = error.issues.map((err)=>`${err.path.join('.')}: ${err.message}`).join(', ');\n                return errorResponse(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR');\n            }\n            if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_0__.Prisma.PrismaClientKnownRequestError) {\n                switch(error.code){\n                    case 'P2002':\n                        return errorResponse('A record with this data already exists', 409, 'DUPLICATE_RECORD');\n                    case 'P2025':\n                        return errorResponse('Record not found', 404, 'NOT_FOUND');\n                    case 'P2003':\n                        return errorResponse('Foreign key constraint failed', 400, 'FOREIGN_KEY_ERROR');\n                    default:\n                        return errorResponse('Database error occurred', 500, 'DATABASE_ERROR');\n                }\n            }\n            return errorResponse('Internal server error', 500, 'INTERNAL_ERROR');\n        }\n    };\n}\n// Method validation helper\nfunction validateMethod(request, allowedMethods) {\n    if (!allowedMethods.includes(request.method)) {\n        throw new ApiError(`Method ${request.method} not allowed`, 405, 'METHOD_NOT_ALLOWED');\n    }\n}\n// Authentication helper using NextAuth\nasync function requireAuth(request) {\n    const { getServerSession } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/next-auth\"), __webpack_require__.e(\"vendor-chunks/@babel\"), __webpack_require__.e(\"vendor-chunks/jose\"), __webpack_require__.e(\"vendor-chunks/openid-client\"), __webpack_require__.e(\"vendor-chunks/oauth\"), __webpack_require__.e(\"vendor-chunks/object-hash\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/preact-render-to-string\"), __webpack_require__.e(\"vendor-chunks/lru-cache\"), __webpack_require__.e(\"vendor-chunks/cookie\"), __webpack_require__.e(\"vendor-chunks/@panva\"), __webpack_require__.e(\"vendor-chunks/oidc-token-hash\")]).then(__webpack_require__.bind(__webpack_require__, /*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\"));\n    const { authOptions } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next-auth\"), __webpack_require__.e(\"vendor-chunks/bcryptjs\"), __webpack_require__.e(\"_rsc_src_services_auth_auth-config_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/services/auth/auth-config */ \"(rsc)/./src/services/auth/auth-config.ts\"));\n    const session = await getServerSession(authOptions);\n    if (!session || !session.user) {\n        throw new ApiError('Authentication required', 401, 'UNAUTHORIZED');\n    }\n    return {\n        id: session.user.id,\n        email: session.user.email,\n        role: session.user.role,\n        name: session.user.name\n    };\n}\n// Admin authorization helper\nasync function requireAdmin(request) {\n    const user = await requireAuth(request);\n    if (user.role !== 'ADMIN') {\n        throw new ApiError('Admin access required', 403, 'FORBIDDEN');\n    }\n    return user;\n}\n// Slug generation helper\nfunction generateSlug(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n// Note: File upload helpers moved to @/services/file-upload/file-upload\n// Search helpers\nfunction buildSearchQuery(searchTerm, fields) {\n    if (!searchTerm) return {};\n    // For case-insensitive search, we'll use the original search term\n    // Prisma will handle case sensitivity based on the database collation\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: searchTerm,\n                    mode: 'insensitive' // This works with PostgreSQL and MySQL, for SQLite it's ignored but still works\n                }\n            }))\n    };\n}\n// Sort helpers\nfunction buildSortQuery(sortBy, sortOrder = 'desc') {\n    if (!sortBy) return {\n        createdat: sortOrder\n    };\n    return {\n        [sortBy]: sortOrder\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/api/api-utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();