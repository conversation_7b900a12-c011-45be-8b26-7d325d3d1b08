'use client'

// Imports
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BuildingOfficeIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeSlashIcon,
  PencilIcon,
  TrashIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { ClientModal } from './client-form-modal'
import { EnhancedProjectManagement } from './project-management'
import { InvoicesManagement } from './invoices-management'
import { PaymentsManagement } from './payments-management'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { SampleModal } from '../shared/sample-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'
import { useNotifications } from '@/components/providers/notification-provider'

// ========================================
// UTILITY FUNCTIONS
// ========================================

// Date formatting utility - consistent date display across the app
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// Generate consistent background colors for avatars based on company name
const getBackgroundColor = (name: string): string => {
  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500', 'bg-indigo-500',
    'bg-yellow-500', 'bg-red-500', 'bg-teal-500', 'bg-orange-500', 'bg-cyan-500'
  ]
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return colors[Math.abs(hash) % colors.length]
}

// Sort icon helper - shows current sort direction for table headers
const getSortIcon = (field: string, sortBy: string, sortOrder: 'asc' | 'desc') => {
  if (sortBy !== field) {
    return <ArrowUpIcon className="h-3 w-3 text-gray-400" />
  }
  return sortOrder === 'asc' 
    ? <ArrowUpIcon className="h-3 w-3 text-blue-600" />
    : <ArrowDownIcon className="h-3 w-3 text-blue-600" />
}

// ========================================
// COMPONENT TYPES AND INTERFACES
// ========================================

// ClientAvatar component props - handles company logo display with fallbacks
interface ClientAvatarProps {
  client: {
    id: string
    companyName: string
    logoUrl?: string
    [key: string]: any
  }
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full-height'
  className?: string
  style?: React.CSSProperties
}

// Size mapping for consistent avatar dimensions
const sizeClasses = {
  xs: 'w-4 h-4 text-[8px]',
  sm: 'w-6 h-6 text-[10px]',
  md: 'w-8 h-8 text-xs',
  lg: 'w-16 h-16 text-xl',
  xl: 'w-20 h-20 text-2xl',
  '2xl': 'w-24 h-24 text-3xl',
  '3xl': 'w-32 h-32 text-4xl',
  'full-height': 'w-40 h-full text-5xl'
}

// ========================================
// COMPONENTS
// ========================================

// ClientAvatar - Memoized component for company logo display with loading states
const ClientAvatar = React.memo(({
  client,
  size = 'md',
  className = '',
  style = {}
}: ClientAvatarProps) => {
  // State for image loading and error handling
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  // Generate initials from company name - memoized for performance
  const initials = useMemo(() => 
    client.companyName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2), [client.companyName]
  )

  // Image event handlers - memoized to prevent unnecessary re-renders
  const handleImageLoad = useCallback(() => setImageLoading(false), [])
  const handleImageError = useCallback(() => {
    setImageError(true)
    setImageLoading(false)
  }, [])

  // Determine styling based on size
  const isFullHeight = size === 'full-height'
  const borderRadius = isFullHeight ? 'rounded-xl' : 'rounded-full'

  // Render logo with loading state
  if (client.logoUrl && !imageError) {
    return (
      <div className={`relative ${className.includes('w-full') ? 'w-full h-full' : sizeClasses[size]} ${className}`} style={style}>
        {imageLoading && (
          <div className={`absolute inset-0 bg-blue-600 ${borderRadius} flex items-center justify-center text-white font-semibold ${className.includes('w-full') ? 'w-full h-full' : sizeClasses[size]}`}>
            {initials}
          </div>
        )}
        <img
          src={client.logoUrl}
          alt={`${client.companyName} logo`}
          className={`${className.includes('w-full') ? 'w-full h-full' : sizeClasses[size]} ${borderRadius} object-cover border-2 border-gray-200 shadow-sm ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={style}
        />
      </div>
    )
  }

  // Special case for full-height display
  if (isFullHeight) {
    return client.logoUrl && !imageError ? (
      <img
        src={client.logoUrl}
        alt={client.companyName}
        className={`w-full h-full object-cover rounded-lg ${className || ''}`}
        style={{ ...style, minHeight: '320px', minWidth: '100%' }}
      />
    ) : null
  }

  // Fallback to colored initials when no logo available
  return (
    <div className={`${className.includes('w-full') ? 'w-full h-full' : sizeClasses[size]} ${getBackgroundColor(client.companyName)} ${borderRadius} flex items-center justify-center text-white font-semibold ${className}`} style={style}>
      {initials || <BuildingOfficeIcon className="w-1/2 h-1/2" />}
    </div>
  )
})

// ========================================
// TYPE DEFINITIONS
// ========================================

// Core data interfaces for the application
interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  _count?: {
    projects: number
    contracts: number
    invoices: number
    payments: number
  }
}

interface Project {
  id: string | number
  clientid: string | number  // Note: using clientid to match child components
  name: string
  description: string
  status?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  estimateeffort?: string
  projgoals?: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  projmanager?: string | number
  orderid?: string | number
  isfeatured?: boolean
  ispublic?: boolean
  displayorder?: number
  createdat: string
  updatedat?: string
  _count?: {
    contracts: number
    invoices: number
    messages: number
    tasks?: number
    projectdocuments?: number
  }
}

interface Invoice {
  id: string | number
  clientid: string | number  // Note: using clientid to match child components
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
}

// Union types for component state management
type ActiveSection = 'clients' | 'projects' | 'invoices' | 'payments'
type ViewMode = 'list' | 'grid'
type DisplayDensity = 'compact' | 'comfortable' | 'spacious'

// ========================================
// MAIN COMPONENT
// ========================================

export function ClientsManagement() {
  // ========================================
  // NOTIFICATIONS & EXTERNAL SERVICES
  // ========================================
  
  // Centralized notification system for consistent user feedback
  const { showSuccess, showError, showWarning, showInfo, showLoading, clearLoadingNotifications } = useNotifications()
  
  // ========================================
  // STATE MANAGEMENT
  // ========================================
  
  // Navigation state - tracks current section and selected items
  const [activeSection, setActiveSection] = useState<ActiveSection>('clients')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [invoiceCount, setInvoiceCount] = useState<number>(0)

  // Data state - core application data
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentFilters, setCurrentFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showWindowList, setShowWindowList] = useState(false)

  // View controls - UI customization options
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [gridColumns, setGridColumns] = useState(3)
  const [density, setDensity] = useState<DisplayDensity>('compact')
  
  // Layout calculations for responsive dropdowns
  const [dropdownSpaceNeeded, setDropdownSpaceNeeded] = useState(0)
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>({
    companyName: true,
    contactName: true,
    contactEmail: true,
    contactPhone: true,
    city: true,
    updatedAt: true,
    isActive: true,
    actions: true
  })
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showColumnControls, setShowColumnControls] = useState(false)

  // Bulk operations state
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  // Modal state management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isSampleModalOpen, setIsSampleModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  
  // Confirmation modal state - complex object for delete confirmations
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean
    title: string
    message: string
    details?: string
    type: 'danger' | 'warning' | 'info' | 'success' | 'verification'
    onConfirm: () => void
    onCancel: () => void
    verificationData?: {
      canDelete: boolean
      reason?: string
      projects?: number
      invoices?: number
      payments?: number
      dependencies?: string[]
    }
    showVerification?: boolean
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'danger',
    onConfirm: () => {},
    onCancel: () => {}
  })
  
  // Refs for DOM manipulation
  const searchInputRef = useRef<HTMLInputElement>(null)

  // ========================================
  // EFFECTS
  // ========================================
  
  // Debounce search query to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch invoice count for selected project - memoized to prevent unnecessary calls
  const fetchInvoiceCount = useCallback(async (projectId: string) => {
    try {
      const response = await fetch(`/api/admin/projects/${projectId}/invoices`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setInvoiceCount(data.data?.length || 0)
        }
      }
    } catch (error) {
      console.error('Error fetching invoice count:', error)
      setInvoiceCount(0)
    }
  }, [])

  // Main data fetching function - memoized with dependencies
  const fetchClients = useCallback(async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
        showInfo('Loading Clients', 'Retrieving client data...')
      }

      // Build query parameters
      const params = new URLSearchParams({
        limit: '100', // Get all clients (max 100)
        sortBy,
        sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
      })

      // Add active filters to query
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })

      const response = await fetch(`/api/admin/clients?${params}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        const clientsData = data.data || []
        setClients(clientsData)
        setError(null)
        if (!preserveFocus) {
          showSuccess('Clients Loaded', `Loaded ${clientsData.length} client${clientsData.length === 1 ? '' : 's'}`)
        }
      } else {
        throw new Error(data.error || 'Failed to fetch clients')
      }
    } catch (err) {
      console.error('Error fetching clients:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setClients([])
      if (!preserveFocus) {
        showError('Failed to Load Clients', 'Unable to retrieve clients')
      }
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery, sortBy, sortOrder, currentFilters, showInfo, showSuccess, showError])

  // Trigger data fetch when search query changes
  useEffect(() => {
    const isSearching = debouncedSearchQuery !== ''
    fetchClients(isSearching)
  }, [fetchClients, debouncedSearchQuery])

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchQuery, currentFilters])

  // Calculate dropdown space needed for proper layout - prevents content overlap
  useEffect(() => {
    let spaceNeeded = 0
    
    if (showColumnSelector) {
      spaceNeeded = Math.max(spaceNeeded, 200) // Column selector dropdown height
    }
    
    if (showWindowList) {
      spaceNeeded = Math.max(spaceNeeded, 120) // Density dropdown height
    }
    
    if (showFilters) {
      spaceNeeded = Math.max(spaceNeeded, 300) // Filters dropdown height
    }
    
    setDropdownSpaceNeeded(spaceNeeded)
  }, [showColumnSelector, showWindowList, showFilters])

  // Global event listener for cross-component navigation
  useEffect(() => {
    const handleOpenItemModal = (event: CustomEvent) => {
      const { type, id, action } = event.detail
      
      if (type === 'client') {
        // Find the client by ID and open edit modal
        const client = clients.find(c => c.id === parseInt(id))
        if (client) {
          setEditingClient(client)
          setIsEditModalOpen(true)
        }
      } else if (type === 'invoice') {
        // Switch to invoices tab and open invoice modal
        setActiveSection('invoices')
        // You can add logic here to find and open specific invoice
      } else if (type === 'payment') {
        // Switch to payments tab and open payment modal
        setActiveSection('payments')
        // You can add logic here to find and open specific payment
      }
    }

    window.addEventListener('openItemModal', handleOpenItemModal as EventListener)
    
    return () => {
      window.removeEventListener('openItemModal', handleOpenItemModal as EventListener)
    }
  }, [clients])

  // ========================================
  // CRUD OPERATIONS
  // ========================================
  
  // Create new client - handles form submission and API call
  const handleCreate = useCallback(async (formData: any) => {
    try {
      console.log('Clients Management - Creating client with data:', formData)
      
      const response = await fetch('/api/admin/clients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      console.log('Clients Management - Response status:', response.status)
      
      if (!response.ok) {
        let errorData = {}
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.error('Clients Management - Failed to parse error response:', parseError)
          errorData = { error: `HTTP ${response.status}: ${response.statusText}` }
        }
        console.error('Clients Management - Error response:', errorData)
        console.error('Clients Management - Response status:', response.status)
        console.error('Clients Management - Response headers:', Object.fromEntries(response.headers.entries()))
        throw new Error((errorData as any).error || `Failed to create client (${response.status})`)
      }

      const result = await response.json()
      console.log('Clients Management - Success response:', result)
      
      setIsCreateModalOpen(false)
      fetchClients()
      showSuccess('Client Created', 'Client created successfully')
    } catch (err) {
      console.error('Clients Management - Create error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client'
      setError(errorMessage)
      showError('Failed to Create Client', errorMessage)
      throw err
    }
  }, [fetchClients, showSuccess, showError])

  // Update existing client - handles form submission and API call
  const handleUpdate = useCallback(async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to update client (${response.status})`)
      }

      setIsEditModalOpen(false)
      setEditingClient(null)
      fetchClients()
      showSuccess('Client Updated', 'Client updated successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client'
      setError(errorMessage)
      showError('Failed to Update Client', errorMessage)
      throw err
    }
  }, [fetchClients, showSuccess, showError])

  // Delete client - handles deletion with loading state
  const handleDelete = useCallback(async (id: string) => {
    try {
      showLoading('Deleting Client', 'Removing client...')
      
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      })

      const data = await response.json()
      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete client')
      }

      setError(null)
      fetchClients()
      showSuccess('Client Deleted', 'Client deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client'
      setError(errorMessage)
      showError('Failed to Delete Client', errorMessage)
      console.error('Delete error:', err)
    }
  }, [fetchClients, showLoading, showSuccess, showError])

  // ========================================
  // DEPENDENCY CHECKING & VALIDATION
  // ========================================
  
  // Check client dependencies before deletion - prevents orphaned data
  const checkClientDependencies = useCallback(async (client: Client) => {
    try {
      showLoading('Verifying Client', `Checking "${client.companyName}"...`)
      
      // Fetch fresh data from API to get current dependency counts
      const response = await fetch(`/api/admin/clients/${client.id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch client details')
      }
      
      const data = await response.json()
      const freshClient = data.data
      
      // Check for projects, invoices, and payments using fresh data
      const projectsCount = freshClient._count?.projects || 0
      const invoicesCount = freshClient._count?.invoices || 0
      const paymentsCount = freshClient._count?.payments || 0
      
      const dependencies: string[] = []
      let canDelete = true
      let reason = ''
      
      if (projectsCount > 0) {
        dependencies.push(`${projectsCount} project${projectsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Client has associated projects that must be removed first'
      }
      
      if (invoicesCount > 0) {
        dependencies.push(`${invoicesCount} invoice${invoicesCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Client has associated invoices that must be removed first'
      }
      
      if (paymentsCount > 0) {
        dependencies.push(`${paymentsCount} payment${paymentsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Client has associated payments that must be removed first'
      }
      
      if (canDelete) {
        reason = 'No dependencies found - safe to delete'
      }
      
      const result = {
        canDelete,
        reason,
        projects: projectsCount,
        invoices: invoicesCount,
        payments: paymentsCount,
        dependencies
      }
      
      return result
    } catch (error) {
      console.error('Error checking client dependencies:', error)
      return {
        canDelete: false,
        reason: 'Error checking dependencies',
        dependencies: ['Unable to verify dependencies']
      }
    }
  }, [showLoading])

  // ========================================
  // CONFIRMATION MODALS
  // ========================================
  
  // Show delete confirmation with dependency checking
  const showDeleteConfirmation = useCallback(async (client: Client) => {
    // Set the client to delete
    setClientToDelete(client)
    
    // Check dependencies first
    const verificationData = await checkClientDependencies(client)
    
    setConfirmationModal({
      isOpen: true,
      title: 'Delete Client',
      message: verificationData.canDelete 
        ? `Are you sure you want to delete "${client.companyName}"?`
        : `Cannot delete "${client.companyName}"`,
      details: verificationData.canDelete
        ? 'This action cannot be undone and will permanently remove the client and all associated data.'
        : 'Please remove all dependencies before attempting to delete this client.',
      type: verificationData.canDelete ? 'danger' : 'verification',
      verificationData,
      showVerification: true,
      onConfirm: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        setClientToDelete(null)
        if (verificationData.canDelete) {
          handleDelete(String(client.id))
        }
      },
      onCancel: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        setClientToDelete(null)
      }
    })
  }, [handleDelete, checkClientDependencies])

  // ========================================
  // DATA PROCESSING & FILTERING
  // ========================================
  
  // Filtered and paginated clients - memoized for performance
  const filteredClients = useMemo(() => {
    let filtered = clients

    // Apply search filter across multiple fields
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase()
      filtered = filtered.filter(client =>
        client.companyName.toLowerCase().includes(query) ||
        client.contactName.toLowerCase().includes(query) ||
        client.contactEmail.toLowerCase().includes(query) ||
        client.city?.toLowerCase().includes(query)
      )
    }

    // Apply other filters
    Object.entries(currentFilters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(client => {
          switch (key) {
            case 'status':
              return client.isActive === (value === 'active')
            case 'city':
              return client.city === value
            default:
              return true
          }
        })
      }
    })

    return filtered
  }, [clients, debouncedSearchQuery, currentFilters])

  // Pagination calculations - memoized to prevent unnecessary recalculations
  const paginationData = useMemo(() => {
    const totalPages = Math.max(1, Math.ceil(filteredClients.length / itemsPerPage))
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedClients = filteredClients.slice(startIndex, endIndex)
    
    return { totalPages, startIndex, endIndex, paginatedClients }
  }, [filteredClients, currentPage, itemsPerPage])

  const { totalPages, startIndex, endIndex, paginatedClients } = paginationData

  // Ensure currentPage is valid when totalPages changes
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages)
    }
  }, [currentPage, totalPages])

  // ========================================
  // SELECTION & PAGINATION HANDLERS
  // ========================================
  
  // Individual client selection - toggles selection state
  const handleSelectClient = useCallback((clientId: string) => {
    setSelectedClients(prev => {
      const newSelection = prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  // Select all clients - toggles between all and none
  const handleSelectAll = useCallback(() => {
    if (selectedClients.length === filteredClients.length) {
      setSelectedClients([])
      setShowBulkActions(false)
    } else {
      setSelectedClients(filteredClients.map(client => String(client.id)))
      setShowBulkActions(true)
    }
  }, [selectedClients.length, filteredClients])

  // Clear all selections
  const handleClearSelection = useCallback(() => {
    setSelectedClients([])
    setShowBulkActions(false)
  }, [])

  // Pagination handlers - memoized for performance
  const handlePageChange = useCallback((page: number) => {
    // Basic validation - will be further validated by useEffect
    if (page >= 1) {
      setCurrentPage(page)
    }
  }, [])

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1) // Reset to first page when changing items per page
  }, [])

  // ========================================
  // STATUS & BULK OPERATIONS
  // ========================================
  
  // Toggle client active status - handles individual status changes
  const handleToggleStatus = useCallback(async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) throw new Error('Failed to update client status')
      fetchClients()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update client status')
    }
  }, [fetchClients])

  // Handle bulk actions - activate, deactivate, or delete multiple clients
  const handleBulkAction = useCallback(async (action: string, clientIds: string[]) => {
    try {
      let endpoint = '/api/admin/clients'
      let method = 'PUT'
      let body: any = { ids: clientIds }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: clientIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} clients`)
      }

      const result = await response.json()
      if (result.success) {
        setSelectedClients([])
        fetchClients()
      } else {
        throw new Error(result.error || `Failed to ${action} clients`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }, [fetchClients])

  // Bulk delete confirmation with dependency checking for multiple clients
  const showBulkDeleteConfirmation = useCallback(async (clientIds: string[]) => {
    const count = clientIds.length
    const clientsToDelete = clients.filter(client => clientIds.includes(String(client.id)))
    
    try {
      showLoading('Verifying Clients', `Checking dependencies for ${clientsToDelete.length} clients...`)
      
      // Check dependencies for each client in parallel
      const verificationPromises = clientsToDelete.map(async (client) => {
        try {
          const response = await fetch(`/api/admin/clients/${client.id}`)
          if (response.ok) {
            const clientData = await response.json()
            const freshClient = clientData.data
            
            const projectsCount = freshClient._count?.projects || 0
            const invoicesCount = freshClient._count?.invoices || 0
            const paymentsCount = freshClient._count?.payments || 0
            
            return {
              client,
              canDelete: projectsCount === 0 && invoicesCount === 0 && paymentsCount === 0,
              projects: projectsCount,
              invoices: invoicesCount,
              payments: paymentsCount,
              dependencies: [
                ...(projectsCount > 0 ? [`${projectsCount} project${projectsCount === 1 ? '' : 's'}`] : []),
                ...(invoicesCount > 0 ? [`${invoicesCount} invoice${invoicesCount === 1 ? '' : 's'}`] : []),
                ...(paymentsCount > 0 ? [`${paymentsCount} payment${paymentsCount === 1 ? '' : 's'}`] : [])
              ]
            }
          }
          return {
            client,
            canDelete: false,
            projects: 0,
            invoices: 0,
            payments: 0,
            dependencies: ['Unable to verify dependencies']
          }
        } catch (error) {
          return {
            client,
            canDelete: false,
            projects: 0,
            invoices: 0,
            payments: 0,
            dependencies: ['Verification failed']
          }
        }
      })
      
      const verificationResults = await Promise.all(verificationPromises)
      
      // Calculate overall verification data
      const totalProjects = verificationResults.reduce((sum, result) => sum + result.projects, 0)
      const totalInvoices = verificationResults.reduce((sum, result) => sum + result.invoices, 0)
      const totalPayments = verificationResults.reduce((sum, result) => sum + result.payments, 0)
      const canDeleteAll = verificationResults.every(result => result.canDelete)
      
      // Collect all dependencies
      const allDependencies = verificationResults.flatMap(result => result.dependencies)
      const uniqueDependencies = [...new Set(allDependencies)]
      
      const verificationData = {
        canDelete: canDeleteAll,
        reason: canDeleteAll 
          ? 'No dependencies found - safe to delete all clients'
          : 'Some clients have dependencies that must be removed first',
        projects: totalProjects,
        invoices: totalInvoices,
        payments: totalPayments,
        dependencies: uniqueDependencies
      }
      
      setConfirmationModal({
        isOpen: true,
        title: count === 1 ? 'Delete Client' : 'Delete Multiple Clients',
        message: verificationData.canDelete 
          ? (count === 1 
              ? `Are you sure you want to delete "${clientsToDelete[0].companyName}"?`
              : `Are you sure you want to delete ${count} clients?`)
          : (count === 1 
              ? `Cannot delete "${clientsToDelete[0].companyName}"`
              : `Cannot delete ${count} clients`),
        details: verificationData.canDelete
          ? 'This action cannot be undone and will permanently remove all selected clients and their associated data.'
          : 'Please remove all dependencies before attempting to delete these clients.',
        type: verificationData.canDelete ? 'danger' : 'verification',
        verificationData,
        showVerification: true,
        onConfirm: () => {
          setConfirmationModal(prev => ({ ...prev, isOpen: false }))
          if (verificationData.canDelete) {
            handleBulkAction('delete', clientIds)
          }
        },
        onCancel: () => {
          setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        }
      })
    } catch (error) {
      console.error('Error verifying clients for bulk delete:', error)
      showError('Verification Failed', 'Unable to verify client dependencies')
    }
  }, [handleBulkAction, clients, showLoading, showError])


  // ========================================
  // VIEW CONTROLS & FILTERS
  // ========================================
  
  // Sort handling - toggles between asc/desc for same field
  const handleSort = useCallback((field: string) => {
    setSortBy(field)
    setSortOrder(prev => sortBy === field && prev === 'asc' ? 'desc' : 'asc')
  }, [sortBy])

  // Column visibility toggle
  const handleColumnToggle = useCallback((columnKey: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }))
  }, [])

  // Filter configuration - defines available filter options
  const filters = [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    },
    {
      key: 'city',
      label: 'City',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Cities' },
        { value: 'New York', label: 'New York' },
        { value: 'Los Angeles', label: 'Los Angeles' },
        { value: 'Chicago', label: 'Chicago' },
        { value: 'Houston', label: 'Houston' }
      ]
    }
  ]

  // Reset all view settings to defaults
  const resetViewSettings = useCallback(() => {
    setVisibleColumns({
      companyName: true,
      contactName: true,
      contactEmail: true,
      contactPhone: true,
      city: true,
      updatedAt: true,
      isActive: true,
      actions: true
    })
    setViewMode('list')
    setDensity('compact')
    setSortBy('updatedAt')
    setSortOrder('desc')
    setCurrentFilters({})
  }, [])

  // ========================================
  // CONSTANTS & CONFIGURATION
  // ========================================
  
  // Available table columns configuration
  const availableColumns = useMemo(() => [
    { key: 'companyName', label: 'Company Name', hideable: false },
    { key: 'contactName', label: 'Contact Name', hideable: true },
    { key: 'contactEmail', label: 'Email', hideable: true },
    { key: 'contactPhone', label: 'Phone', hideable: true },
    { key: 'website', label: 'Website', hideable: true },
    { key: 'city', label: 'City', hideable: true },
    { key: 'country', label: 'Country', hideable: true },
    { key: 'updatedAt', label: 'Last Updated', hideable: true },
    { key: 'isActive', label: 'Status', hideable: true },
  ], [])

  // View mode icons mapping
  const viewModeIcons = useMemo(() => ({
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }), [])

  // Density labels for UI display
  const densityLabels = useMemo(() => ({
    compact: 'Compact',
    comfortable: 'Comfortable',
  }), [])

  // Navigation sections configuration - defines the main app sections
  const sections = useMemo(() => [
    {
      id: 'clients' as const,
      title: 'Clients',
      description: 'Manage client information and contacts',
      color: 'bg-blue-500',
      isActive: activeSection === 'clients',
      selectedName: selectedClient?.companyName || null
    },
    {
      id: 'projects' as const,
      title: 'Projects',
      description: 'Manage client projects and deliverables',
      color: 'bg-green-500',
      isActive: activeSection === 'projects',
      disabled: !selectedClient,
      selectedName: selectedProject?.name || null
    },
    {
      id: 'invoices' as const,
      title: 'Invoices',
      description: 'Manage project invoices and billing',
      color: 'bg-yellow-500',
      isActive: activeSection === 'invoices',
      disabled: !selectedProject,
      selectedName: selectedInvoice ? `Invoice #${selectedInvoice.id}` : null
    },
    {
      id: 'payments' as const,
      title: 'Payments',
      description: 'Manage invoice payments and transactions',
      color: 'bg-purple-500',
      isActive: activeSection === 'payments',
      disabled: !selectedInvoice,
      selectedName: selectedPayment ? `Payment #${selectedPayment.id}` : null
    }
  ], [activeSection, selectedClient, selectedProject, selectedInvoice, selectedPayment])

  // ========================================
  // NAVIGATION HANDLERS
  // ========================================
  
  // Handle section navigation - prevents navigation to disabled sections
  const handleSectionChange = useCallback((sectionId: ActiveSection) => {
    const section = sections.find(s => s.id === sectionId)
    if (!section?.disabled) {
      setActiveSection(sectionId)
    }
  }, [sections])

  // Handle client selection with data sanitization
  const handleClientSelect = useCallback((client: Client | null) => {
    const cleanClient = client ? {
      id: String(client.id),
      companyName: String(client.companyName || ''),
      contactName: String(client.contactName || ''),
      contactEmail: String(client.contactEmail || ''),
      contactPhone: client.contactPhone ? String(client.contactPhone) : undefined,
      website: client.website ? String(client.website) : undefined,
      address: client.address ? String(client.address) : undefined,
      city: client.city ? String(client.city) : undefined,
      state: client.state ? String(client.state) : undefined,
      country: client.country ? String(client.country) : undefined,
      zipCode: client.zipCode ? String(client.zipCode) : undefined,
      logoUrl: client.logoUrl ? String(client.logoUrl) : undefined,
      isActive: Boolean(client.isActive),
      notes: client.notes ? String(client.notes) : undefined,
      createdAt: client.createdAt ? String(client.createdAt) : '',
      updatedAt: client.updatedAt ? String(client.updatedAt) : '',
      _count: client._count || undefined
    } : null

    setSelectedClient(cleanClient)
    setSelectedProject(null)
    setSelectedInvoice(null)
    setSelectedPayment(null)
    setInvoiceCount(0)
    if (cleanClient && activeSection === 'clients') {
      setActiveSection('projects')
    }
  }, [activeSection])

  // ========================================
  // ACTION HANDLERS
  // ========================================
  
  // Handle individual client actions - centralized action dispatcher
  const handleAction = useCallback(async (action: string, item: Client) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          window.open(`/admin/clients/${item.id}`, '_blank')
          break
        case 'edit':
          setEditingClient(item)
          setIsEditModalOpen(true)
          showInfo('Edit Client', `Editing client: "${item.companyName}"`)
          break
        case 'toggle-status':
          await handleToggleStatus(String(item.id), !item.isActive)
          break
        case 'delete':
          showDeleteConfirmation(item)
          break
        case 'select':
          handleClientSelect(item)
          break
        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }, [handleToggleStatus, showDeleteConfirmation, handleClientSelect, showInfo])

  // Handle project selection with data sanitization
  const handleProjectSelect = useCallback((project: Project | null) => {
    const cleanProject = project ? {
      id: String(project.id),
      clientid: String(project.clientid),  // Note: using clientid to match child components
      name: String(project.name || ''),
      description: String(project.description || ''),
      status: project.status ? String(project.status) : undefined,
      projstartdate: project.projstartdate ? String(project.projstartdate) : undefined,
      projcompletiondate: project.projcompletiondate ? String(project.projcompletiondate) : undefined,
      estimatecost: project.estimatecost ? Number(project.estimatecost) : undefined,
      estimatetime: project.estimatetime ? String(project.estimatetime) : undefined,
      estimateeffort: project.estimateeffort ? String(project.estimateeffort) : undefined,
      projgoals: project.projgoals ? String(project.projgoals) : undefined,
      imageurl: project.imageurl ? String(project.imageurl) : undefined,
      projecturl: project.projecturl ? String(project.projecturl) : undefined,
      githuburl: project.githuburl ? String(project.githuburl) : undefined,
      tags: project.tags ? String(project.tags) : undefined,
      projmanager: project.projmanager ? String(project.projmanager) : undefined,
      orderid: project.orderid ? String(project.orderid) : undefined,
      isfeatured: project.isfeatured || false,
      ispublic: project.ispublic || false,
      displayorder: project.displayorder || 0,
      createdat: project.createdat ? String(project.createdat) : '',
      updatedat: project.updatedat ? String(project.updatedat) : undefined,
      _count: project._count || undefined
    } : null

    setSelectedProject(cleanProject)
    setSelectedInvoice(null)
    
    // Fetch invoice count for the selected project
    if (cleanProject) {
      fetchInvoiceCount(cleanProject.id.toString())
    } else {
      setInvoiceCount(0)
    }
    
    if (cleanProject && activeSection === 'projects') {
      setActiveSection('invoices')
    }
  }, [activeSection, fetchInvoiceCount])



  // Handle invoice selection with data sanitization
  const handleInvoiceSelect = useCallback((invoice: Invoice | null) => {
    const cleanInvoice = invoice ? {
      id: String(invoice.id),
      clientid: String(invoice.clientid),  // Note: using clientid to match child components
      projectId: invoice.projectId ? String(invoice.projectId) : undefined,
      totalAmount: Number(invoice.totalAmount || 0),
      subtotal: invoice.subtotal ? Number(invoice.subtotal) : undefined,
      taxRate: Number(invoice.taxRate || 0),
      taxAmount: Number(invoice.taxAmount || 0),
      status: String(invoice.status || ''),
      dueDate: String(invoice.dueDate || ''),
      description: invoice.description ? String(invoice.description) : undefined,
      paidAt: invoice.paidAt ? String(invoice.paidAt) : undefined,
      createdAt: invoice.createdAt ? String(invoice.createdAt) : '',
      updatedAt: invoice.updatedAt ? String(invoice.updatedAt) : undefined,
      _count: invoice._count || undefined
    } : null

    setSelectedInvoice(cleanInvoice)
    setSelectedPayment(null)
    if (cleanInvoice && activeSection === 'invoices') {
      setActiveSection('payments')
    }
  }, [activeSection])

  // Handle payment selection with data sanitization
  const handlePaymentSelect = useCallback((payment: Payment | null) => {
    const cleanPayment = payment ? {
      id: String(payment.id),
      amount: Number(payment.amount || 0),
      paymentDate: String(payment.paymentDate || ''),
      paymentMethod: String(payment.paymentMethod || ''),
      status: String(payment.status || ''),
      notes: payment.notes ? String(payment.notes) : undefined,
      invoiceId: String(payment.invoiceId),
      createdAt: payment.createdAt ? String(payment.createdAt) : '',
      updatedAt: payment.updatedAt ? String(payment.updatedAt) : undefined
    } : null

    setSelectedPayment(cleanPayment)
  }, [])


  // ========================================
  // STATISTICS & COUNTERS
  // ========================================
  
  // Counter function for section statistics - used for both cards and stats
  const getCounter = useCallback((sectionId: string) => {
    switch (sectionId) {
      case 'clients':
        return clients.length
      case 'projects':
        return selectedClient ? (selectedClient._count?.projects || 0) : 0
      case 'invoices':
        // Use the fetched invoice count for the selected project
        return selectedProject ? invoiceCount : 0
      case 'payments':
        // Payments-management only works when an invoice is selected
        return selectedInvoice ? (selectedInvoice._count?.payments || 0) : 0
      default:
        return 0
    }
  }, [clients, selectedClient, selectedProject, selectedInvoice, invoiceCount])

  // Memoized stats data - uses the same counter logic for consistency
  const statsData = useMemo(() => [
    { label: 'Total Clients', value: getCounter('clients'), color: 'text-blue-600', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
    { label: 'Active', value: clients.filter(c => c.isActive).length, color: 'text-green-600', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' },
    { label: 'Projects', value: getCounter('projects'), color: 'text-emerald-600', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
    { label: 'Invoices', value: getCounter('invoices'), color: 'text-amber-600', icon: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z' }
  ], [clients, getCounter])


  // ========================================
  // RENDERING
  // ========================================
  
  return (
    <div className="h-full flex flex-col space-y-4" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
      {/* Header Section */}
      <div className="relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20" />

        <div className="relative p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <BuildingOfficeIcon className="h-14 w-14 text-teal-600 -mt-2" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mt-2">
                  Clients Management
                </h1>
                <p className="text-sm font-medium text-gray-600">
                  Manage your client projects, invoices, and payments
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsSampleModalOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              >
                Sample Modal
              </button>
            </div>

            <div className="hidden lg:flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              <span className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="overflow-visible">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 min-w-[640px] md:min-w-0">
          {sections.map((section) => (
            <motion.button
              key={section.id}
              onClick={() => handleSectionChange(section.id)}
              disabled={section.disabled}
              className={`group relative overflow-visible rounded-lg border transition-all duration-300 text-left ${
                section.isActive
                  ? section.id === 'clients' 
                    ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-500 shadow-lg cursor-pointer'
                    : section.id === 'projects'
                    ? 'bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-500 shadow-lg cursor-pointer'
                    : section.id === 'invoices'
                    ? 'bg-gradient-to-br from-amber-50 to-amber-100 border-amber-500 shadow-lg cursor-pointer'
                    : section.id === 'payments'
                    ? 'bg-gradient-to-br from-purple-50 to-purple-100 border-purple-500 shadow-lg cursor-pointer'
                    : 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-500 shadow-lg cursor-pointer'
                  : section.disabled
                  ? 'border-gray-200 cursor-not-allowed opacity-60 bg-gray-50'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-lg hover:transform hover:scale-102 bg-white hover:bg-gray-50'
              }`}
              whileHover={!section.disabled ? { y: -2 } : undefined}
              whileTap={!section.disabled ? { scale: 0.98 } : undefined}
              aria-label={`Navigate to ${section.title} section`}
              aria-describedby={`${section.id}-description`}
              aria-current={section.isActive ? 'page' : undefined}
            >
              {/* Content */}
              <div className="relative px-3 py-2 rounded-lg">
                {/* Header with Icon, Title and Selected Name */}
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-xl transition-all duration-300 ${
                      section.isActive
                        ? section.id === 'clients' 
                          ? 'bg-blue-100 text-blue-600'
                          : section.id === 'projects'
                          ? 'bg-emerald-100 text-emerald-600'
                          : section.id === 'invoices'
                          ? 'bg-amber-100 text-amber-600'
                          : 'bg-purple-100 text-purple-600'
                        : section.disabled
                        ? 'bg-gray-100 text-gray-400'
                        : 'bg-gray-50 text-gray-500 group-hover:bg-gray-100'
                    }`}>
                      {section.id === 'clients' && <BuildingOfficeIcon className="h-5 w-5" aria-hidden="true" />}
                      {section.id === 'projects' && <DocumentTextIcon className="h-5 w-5" aria-hidden="true" />}
                      {section.id === 'invoices' && <CurrencyDollarIcon className="h-5 w-5" aria-hidden="true" />}
                      {section.id === 'payments' && <CreditCardIcon className="h-5 w-5" aria-hidden="true" />}
                    </div>
                    
                    {/* Counter for non-client cards - between icon and title */}
                    {section.id !== 'clients' && (
                      <div className={`text-lg sm:text-xl font-bold transition-colors duration-300 ${
                        section.isActive
                          ? 'text-gray-900'
                          : section.disabled
                          ? 'text-gray-400'
                          : 'text-gray-700 group-hover:text-gray-900'
                      }`}>
                        ({getCounter(section.id)})
                      </div>
                    )}
                    
                    <h3 className={`text-lg sm:text-xl font-bold transition-colors duration-300 ${
                      section.isActive
                        ? 'text-gray-900'
                        : section.disabled
                        ? 'text-gray-400'
                        : 'text-gray-700 group-hover:text-gray-900'
                    }`}>
                      {String(section.title)}
                    </h3>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-1">
                  <p
                    id={`${section.id}-description`}
                    className={`text-xs sm:text-sm leading-relaxed transition-colors duration-300 ${
                      section.isActive
                        ? 'text-gray-600'
                        : section.disabled
                        ? 'text-gray-400'
                        : 'text-gray-500 group-hover:text-gray-600'
                    }`}
                  >
                    {String(section.description)}
                  </p>
                </div>
                
                {/* Bottom Section: Selected Name (Left) and Row Counter (Right) */}
                <div className="mt-auto flex items-center gap-2 min-w-0">
                  {/* Selected Name - Left Side */}
                  {section.selectedName && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                      className="flex-1 min-w-0"
                    >
                      <div className={`inline-flex items-center px-2 sm:px-3 py-1.5 rounded-md max-w-full ${
                        section.isActive
                          ? section.id === 'clients' 
                            ? 'bg-blue-100/80 text-blue-900'
                            : section.id === 'projects'
                            ? 'bg-emerald-100/80 text-emerald-900'
                            : section.id === 'invoices'
                            ? 'bg-amber-100/80 text-amber-900'
                            : 'bg-purple-100/80 text-purple-900'
                          : section.id === 'clients' 
                            ? 'bg-blue-50/80 text-blue-800'
                            : section.id === 'projects'
                            ? 'bg-emerald-50/80 text-emerald-800'
                            : section.id === 'invoices'
                            ? 'bg-amber-50/80 text-amber-800'
                            : 'bg-purple-50/80 text-purple-800'
                      }`}>
                        <div className="flex items-center space-x-1.5 sm:space-x-2 min-w-0">
                          <div className={`w-1.5 h-1.5 rounded-full flex-shrink-0 ${
                            section.id === 'clients' 
                              ? 'bg-blue-600'
                              : section.id === 'projects'
                              ? 'bg-emerald-600'
                              : section.id === 'invoices'
                              ? 'bg-amber-600'
                              : 'bg-purple-600'
                          }`}></div>
                          <div className="text-xs font-medium truncate min-w-0" title={section.selectedName}>
                            {section.selectedName}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                  
                  {/* Row Counter - Right Side (only for client cards) */}
                  {section.id === 'clients' && (
                    <div className="text-right flex-shrink-0">
                      <div className={`text-xs font-semibold whitespace-nowrap ${
                        section.isActive
                          ? 'text-blue-600'
                          : section.disabled
                          ? 'text-gray-400'
                          : 'text-gray-500'
                      }`}>
                        {`${getCounter(section.id)} Clients`}
                      </div>
                    </div>
                  )}
                </div>
                
              </div>

              {/* CSS Triangle Arrow - show for all cards except payments */}
              {section.id !== 'payments' && (
                <div className={`absolute top-1/2 right-[-20px] transform -translate-y-1/2 w-0 h-0 
                                border-t-[20px] border-t-transparent 
                                border-b-[20px] border-b-transparent 
                                border-l-[20px] ${
                                  section.isActive
                                    ? section.id === 'clients' 
                                      ? 'border-l-blue-50'
                                      : section.id === 'projects'
                                      ? 'border-l-emerald-50'
                                      : section.id === 'invoices'
                                      ? 'border-l-amber-50'
                                      : 'border-l-blue-50'
                                    : 'border-l-white'
                                }`}
                     style={{
                       filter: 'drop-shadow(1px 0 0 #d1d5db)'
                     }}>
                </div>
              )}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div
        className="flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden"
        role="main"
        aria-label={`${sections.find(s => s.isActive)?.title} management section`}
      >
        {activeSection === 'clients' && (
          <div className="space-y-3" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
            {/* Search and View Controls - Responsive Design */}
            <div className="space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
              {/* Mobile Layout - Stacked */}
              <div className="flex flex-col space-y-3 lg:hidden">
                {/* Search Bar - Full Width on Mobile */}
                <div className="w-full">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Search clients..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                  </div>
                </div>

            {/* Mobile Controls - Single Row */}
            <div className="flex items-center gap-1">
              {/* View Mode Toggle - Stretched */}
              <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">List</span>
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">Grid</span>
                </button>
              </div>

              {/* Grid Columns Control (for grid view) */}
              {viewMode === 'grid' && (
                <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                  <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
                  <div className="flex items-center gap-0.5 flex-1">
                    {[1, 2, 3, 4].map((num) => (
                      <button
                        key={num}
                        onClick={() => setGridColumns(num)}
                        className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                          gridColumns === num
                            ? 'bg-white text-green-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title={`${num} column${num > 1 ? 's' : ''}`}
                      >
                        {num}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Table Columns Control (for list view) */}
              {viewMode === 'list' && (
                <div className="relative flex-1">
                  <button
                    onClick={() => setShowColumnSelector(!showColumnSelector)}
                    className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                    title="Columns"
                  >
                    <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                    <span className="hidden xs:inline">Col</span>
                    <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                  </button>
                </div>
              )}

              {/* Filters Button - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                    showFilters || Object.keys(currentFilters).some(key => currentFilters[key])
                      ? 'bg-blue-50 text-blue-700 border-blue-300'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  title="Filters"
                >
                  <FunnelIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">Filter</span>
                  {Object.keys(currentFilters).some(key => currentFilters[key]) && (
                    <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {Object.values(currentFilters).filter(Boolean).length}
                    </span>
                  )}
                </button>
              </div>

              {/* Density Control - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowWindowList(!showWindowList)}
                  className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  title="Density"
                >
                  <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
                  <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                </button>
              </div>

              {/* Create Button - Stretched */}
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Add</span>
              </button>
            </div>
              </div>

              {/* Desktop Layout - Horizontal */}
              <div className="hidden lg:flex items-center justify-between gap-4">
                {/* Search Bar and Filters */}
                <div className="flex items-center gap-3 flex-1 max-w-md">
                  <div className="relative flex-1">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Search clients by name, email, company..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                  </div>
                  
                  {/* Filters Dropdown */}
                  <div className="relative dropdown-container">
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        showFilters || Object.keys(currentFilters).some(key => currentFilters[key])
                          ? 'bg-blue-50 text-blue-700 border-blue-300'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      }`}
                      title="Show/hide filters"
                    >
                      <FunnelIcon className="h-4 w-4 mr-2" />
                      Filters
                      {Object.keys(currentFilters).some(key => currentFilters[key]) && (
                        <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {Object.values(currentFilters).filter(Boolean).length}
                        </span>
                      )}
                      <ChevronDownIcon className="h-4 w-4 ml-2" />
                    </button>

                    {/* Filters Dropdown */}
                    {showFilters && (
                      <div className="absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                            <button
                              onClick={() => setShowFilters(false)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <div className="space-y-4">
                            {filters.map((filter) => (
                              <div key={filter.key}>
                                <label className="block text-xs font-medium text-gray-700 mb-2">
                                  {filter.label}
                                </label>
                                <select
                                  value={currentFilters[filter.key] || ''}
                                  onChange={(e) => {
                                    const newFilters = { ...currentFilters }
                                    if (e.target.value) {
                                      newFilters[filter.key] = e.target.value
                                    } else {
                                      delete newFilters[filter.key]
                                    }
                                    setCurrentFilters(newFilters)
                                  }}
                                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                  {filter.options.map((option) => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            ))}
                          </div>
                          
                          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                            <button
                              onClick={() => {
                                setCurrentFilters({})
                                setShowFilters(false)
                              }}
                              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                            >
                              Clear All
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Desktop Controls */}
                <div className="flex items-center space-x-3">
                  {/* View Mode Toggle */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">View:</span>
                    <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                      <button
                        onClick={() => setViewMode('list')}
                        className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                          viewMode === 'list'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title="List view"
                      >
                        <ListBulletIcon className="h-5 w-5" />
                        <span className="text-sm font-medium">List</span>
                      </button>
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                          viewMode === 'grid'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title="Grid view"
                      >
                        <Squares2X2Icon className="h-5 w-5" />
                        <span className="text-sm font-medium">Grid</span>
                      </button>
                    </div>
                  </div>

                  {/* Grid Columns Control (for grid view) */}
                  {viewMode === 'grid' && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">Columns:</span>
                      <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                        <button
                          onClick={() => setGridColumns(1)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 1
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="1 column"
                        >
                          1
                        </button>
                        <button
                          onClick={() => setGridColumns(2)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 2
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="2 columns"
                        >
                          2
                        </button>
                        <button
                          onClick={() => setGridColumns(3)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 3
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="3 columns"
                        >
                          3
                        </button>
                        <button
                          onClick={() => setGridColumns(4)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 4
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="4 columns"
                        >
                          4
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Table Columns Control (for list view) */}
                  {viewMode === 'list' && (
                    <div className="relative dropdown-container">
                      <button
                        onClick={() => setShowColumnSelector(!showColumnSelector)}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        title="Select columns to display"
                      >
                        <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                        Columns
                        <ChevronDownIcon className="h-4 w-4 ml-2" />
                      </button>

                      {/* Column Selector Dropdown */}
                      {showColumnSelector && (
                        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                          <div className="p-2">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
                            {Object.entries(visibleColumns).map(([key, visible]) => (
                              <label key={key} className="flex items-center space-x-2 py-1">
                                <input
                                  type="checkbox"
                                  checked={visible}
                                  onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="text-sm text-gray-700 capitalize">
                                  {key === 'companyName' ? 'Company Name' :
                                   key === 'contactName' ? 'Contact Name' :
                                   key === 'contactEmail' ? 'Contact Email' :
                                   key === 'contactPhone' ? 'Contact Phone' :
                                   key === 'updatedAt' ? 'Updated' :
                                   key === 'isActive' ? 'Status' :
                                   key === 'actions' ? 'Actions' : key}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Density */}
                  <div className="relative dropdown-container">
                    <button
                      onClick={() => setShowWindowList(!showWindowList)}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      title="Select density"
                    >
                      <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                      {density.charAt(0).toUpperCase() + density.slice(1)}
                      <ChevronDownIcon className="h-4 w-4 ml-2" />
                    </button>

                    {/* Density Dropdown */}
                    {showWindowList && (
                      <div className="absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-1">
                          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                            <button
                              key={option}
                              onClick={() => {
                                setDensity(option)
                                setShowWindowList(false)
                              }}
                              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                              }`}
                            >
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Create Button */}
                  <button
                    onClick={() => setIsCreateModalOpen(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Client
                  </button>
                </div>
              </div>

              {/* Mobile Dropdowns */}
              {/* Filters Dropdown - Mobile */}
              {showFilters && (
                <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    {filters.map((filter) => (
                      <div key={filter.key}>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          {filter.label}
                        </label>
                        <select
                          value={currentFilters[filter.key] || ''}
                          onChange={(e) => {
                            const newFilters = { ...currentFilters }
                            if (e.target.value) {
                              newFilters[filter.key] = e.target.value
                            } else {
                              delete newFilters[filter.key]
                            }
                            setCurrentFilters(newFilters)
                          }}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {filter.options.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setCurrentFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              )}

              {/* Column Selector Dropdown - Mobile */}
              {showColumnSelector && (
                <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
                  {Object.entries(visibleColumns).map(([key, visible]) => (
                    <label key={key} className="flex items-center space-x-2 py-1">
                      <input
                        type="checkbox"
                        checked={visible}
                        onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700 capitalize">
                        {key === 'companyName' ? 'Company Name' :
                         key === 'contactName' ? 'Contact Name' :
                         key === 'contactEmail' ? 'Contact Email' :
                         key === 'contactPhone' ? 'Contact Phone' :
                         key === 'updatedAt' ? 'Updated' :
                         key === 'isActive' ? 'Status' :
                         key === 'actions' ? 'Actions' : key}
                      </span>
                    </label>
                  ))}
                </div>
              )}

              {/* Density Dropdown - Mobile */}
              {showWindowList && (
                <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
                  {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                    <button
                      key={option}
                      onClick={() => {
                        setDensity(option)
                        setShowWindowList(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                        density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                      }`}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </button>
                  ))}
                </div>
              )}
            </div>


            {/* Stats Overview */}
            <div className="px-4 pt-1 pb-1 bg-blue-100">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {statsData.map((stat, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <svg className={`w-8 h-8 ${stat.color}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={stat.icon} />
                    </svg>
                    <div className="flex items-center space-x-2">
                      <div className={`text-3xl font-bold ${stat.color}`}>{stat.value}</div>
                      <div className="text-base text-gray-600">{stat.label}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Bulk Actions Bar - List and Grid Views */}
            {showBulkActions && selectedClients.length > 0 && (viewMode === 'list' || viewMode === 'grid') && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-semibold text-xs">
                          {selectedClients.length}
                    </span>
                  </div>
                      <span className="text-xs font-medium text-blue-900">
                        client{selectedClients.length === 1 ? '' : 's'} selected
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1.5">
                    <button
                      onClick={() => handleBulkAction('activate', selectedClients)}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                        title="Activate selected clients"
                    >
                        <EyeIcon className="h-3 w-3 mr-1" />
                      Activate
                    </button>
                      
                    <button
                      onClick={() => handleBulkAction('deactivate', selectedClients)}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                        title="Deactivate selected clients"
                    >
                      <EyeSlashIcon className="h-3 w-3 mr-1" />
                      Deactivate
                    </button>
                      
                    <button
                      onClick={() => showBulkDeleteConfirmation(selectedClients)}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                        title="Delete selected clients"
                    >
                      <TrashIcon className="h-3 w-3 mr-1" />
                      Delete
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1.5">
                    <button
                      onClick={handleClearSelection}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                      title="Clear selection"
                    >
                      <XMarkIcon className="h-3 w-3 mr-1" />
                      Clear
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content */}
            <div className={viewMode === 'list' ? 'p-4' : ''}>
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : error ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                  <div className="text-red-600 mb-4">
                    <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Clients</h3>
                  <p className="text-sm text-red-700 mb-4">{error}</p>
                  <button
                    onClick={() => fetchClients()}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Try Again
                  </button>
                </div>
              ) : clients.length === 0 ? (
                <div className="text-center py-12">
                  <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No clients found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {debouncedSearchQuery || Object.values(filters).some(v => v)
                      ? 'Try adjusting your search terms or filters.'
                      : 'Get started by adding your first client.'}
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={() => setIsCreateModalOpen(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Client
                    </button>
                  </div>
                </div>
              ) : viewMode === 'list' ? (
                <div className="overflow-x-auto -m-4">
                  <div className="min-w-full inline-block align-middle">
                    <table key="clients-table" className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-200 border-b border-gray-300">
                      <tr>
                        <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                          <input
                            type="checkbox"
                            checked={selectedClients.length === filteredClients.length && filteredClients.length > 0}
                            onChange={handleSelectAll}
                            className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                              density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'
                            }`}
                          />
                        </th>
                        {visibleColumns.companyName && (
                          <th scope="col" className="pl-6 pr-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/6 py-2 text-xs min-w-[200px]" onClick={() => handleSort('companyName')}>
                            <div className="flex items-center space-x-1">
                              <span>Company</span>
                              {getSortIcon('companyName', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.contactName && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/8 py-2 text-xs min-w-[120px] hidden sm:table-cell" onClick={() => handleSort('contactName')}>
                            <div className="flex items-center space-x-1">
                              <span>Contact</span>
                              {getSortIcon('contactName', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.contactEmail && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/4 py-2 text-xs min-w-[180px] hidden md:table-cell" onClick={() => handleSort('contactEmail')}>
                            <div className="flex items-center space-x-1">
                              <span>Email</span>
                              {getSortIcon('contactEmail', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.contactPhone && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/8 py-2 text-xs min-w-[120px] hidden lg:table-cell" onClick={() => handleSort('contactPhone')}>
                            <div className="flex items-center space-x-1">
                              <span>Phone</span>
                              {getSortIcon('contactPhone', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.city && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/12 py-2 text-xs min-w-[100px] hidden xl:table-cell" onClick={() => handleSort('city')}>
                            <div className="flex items-center space-x-1">
                              <span>Location</span>
                              {getSortIcon('city', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.updatedAt && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/12 py-2 text-xs min-w-[100px] hidden lg:table-cell" onClick={() => handleSort('updatedAt')}>
                            <div className="flex items-center space-x-1">
                              <span>Updated</span>
                              {getSortIcon('updatedAt', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.isActive && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/12 py-2 text-xs min-w-[80px] hidden sm:table-cell" onClick={() => handleSort('isActive')}>
                            <div className="flex items-center space-x-1">
                              <span>Status</span>
                              {getSortIcon('isActive', sortBy, sortOrder)}
                            </div>
                          </th>
                        )}
                        {visibleColumns.actions && (
                          <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider w-1/12 py-2 text-xs min-w-[120px]">
                            <span>Actions</span>
                          </th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {paginatedClients.map((client) => (
                        <ClientRow
                          key={client.id}
                          client={client}
                          viewMode={viewMode}
                          displayDensity={density}
                          visibleColumns={visibleColumns}
                          isSelected={selectedClients.includes(String(client.id))}
                          onSelect={() => handleSelectClient(String(client.id))}
                          onAction={(action) => handleAction(action, client)}
                          actionLoading={actionLoading}
                          selectedClient={selectedClient}
                          onClientSelect={handleClientSelect}
                        />
                      ))}
                    </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div>
                  {/* Mobile Card View - Hidden on larger screens */}
                  <div className="block sm:hidden">
                    <div className="space-y-4">
                      {paginatedClients.map((client) => (
                        <div key={`mobile-${client.id}`} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <ClientAvatar client={{ ...client, id: String(client.id) }} size="sm" />
                              <div className="min-w-0 flex-1">
                                <h3 className="text-sm font-medium text-gray-900 truncate">
                                  {client.companyName}
                                </h3>
                                <p className="text-sm text-gray-500 truncate">
                                  {client.contactName}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                client.isActive 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {client.isActive ? 'Active' : 'Inactive'}
                              </span>
                              <div className="flex items-center space-x-1">
                                <button
                                  onClick={() => handleAction('edit', client)}
                                  className="text-blue-600 hover:text-blue-900 p-1"
                                  title="Edit client"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleAction('toggle-status', client)}
                                  className={`p-1 ${
                                    client.isActive
                                      ? 'text-green-600 hover:text-green-900'
                                      : 'text-gray-400 hover:text-gray-600'
                                  }`}
                                  title={client.isActive ? 'Deactivate client' : 'Activate client'}
                                >
                                  {client.isActive ? (
                                    <EyeSlashIcon className="h-4 w-4" />
                                  ) : (
                                    <EyeIcon className="h-4 w-4" />
                                  )}
                                </button>
                                <button
                                  onClick={() => handleAction('delete', client)}
                                  className="text-red-600 hover:text-red-900 p-1"
                                  title="Delete client"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                          <div className="mt-3 space-y-1">
                            {client.contactEmail && (
                              <p className="text-xs text-gray-500 truncate">
                                <span className="font-medium">Email:</span> {client.contactEmail}
                              </p>
                            )}
                            {client.contactPhone && (
                              <p className="text-xs text-gray-500 truncate">
                                <span className="font-medium">Phone:</span> {client.contactPhone}
                              </p>
                            )}
                            {client.city && (
                              <p className="text-xs text-gray-500 truncate">
                                <span className="font-medium">Location:</span> {client.city}
                              </p>
                            )}
                            <p className="text-xs text-gray-500">
                              <span className="font-medium">Updated:</span> {formatDate(client.updatedAt)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Desktop Grid View - Hidden on mobile */}
                  <div className="hidden sm:block">
                    <div key="clients-grid" className={`grid gap-4 ${
                        gridColumns === 1 ? 'grid-cols-1' :
                        gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                        gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                      }`}>
                  {paginatedClients.map((client) => (
                    <ClientRow
                      key={client.id}
                      client={client}
                      viewMode={viewMode}
                      displayDensity={density}
                      visibleColumns={visibleColumns}
                      isSelected={selectedClients.includes(String(client.id))}
                      onSelect={() => handleSelectClient(String(client.id))}
                      onAction={(action) => handleAction(action, client)}
                      actionLoading={actionLoading}
                      selectedClient={selectedClient}
                      onClientSelect={handleClientSelect}
                    />
                  ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Responsive Pagination */}
            {(filteredClients.length > 0 || clients.length > 0) && (
              <ResponsivePagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={handleItemsPerPageChange}
                totalItems={filteredClients.length}
                startIndex={startIndex}
                endIndex={endIndex}
                itemsPerPageOptions={[5, 10, 20, 25, 50, 100]}
                showItemsPerPage={true}
                showPageInfo={true}
              />
            )}

            {/* Modals */}
            <AnimatePresence>
              {isCreateModalOpen && (
                <ClientModal
                  isOpen={isCreateModalOpen}
                  onClose={() => setIsCreateModalOpen(false)}
                  onSubmit={handleCreate}
                  title="Add New Client"
                  showSuccess={showSuccess}
                  showError={showError}
                />
              )}
              {isEditModalOpen && editingClient && (
                <ClientModal
                  isOpen={isEditModalOpen}
                  onClose={() => {
                    setIsEditModalOpen(false)
                    setEditingClient(null)
                  }}
                  onSubmit={(data) => handleUpdate(String(editingClient.id), data)}
                  title="Edit Client"
                  client={editingClient}
                  showSuccess={showSuccess}
                  showError={showError}
                />
              )}
              {isSampleModalOpen && (
                <SampleModal
                  key="sample-modal"
                  isOpen={isSampleModalOpen}
                  onClose={() => setIsSampleModalOpen(false)}
                  title="Sample Modal Demo"
                  message="This is a demonstration of a modal with blue header and transparent text background. The text should appear white with no background interference."
                />
              )}
            </AnimatePresence>
          </div>
        )}

        {activeSection === 'projects' && selectedClient && (
          <EnhancedProjectManagement
            client={selectedClient}
            selectedProject={selectedProject}
            onProjectSelect={handleProjectSelect}
            activeSection={activeSection}
            showSuccess={showSuccess}
            showError={showError}
            showLoading={showLoading}
            showInfo={showInfo}
            clearLoadingNotifications={clearLoadingNotifications}
          />
        )}

        {activeSection === 'invoices' && selectedProject && selectedClient && (
          <InvoicesManagement
            client={selectedClient}
            project={selectedProject}
            selectedInvoice={selectedInvoice}
            onInvoiceSelect={handleInvoiceSelect}
            activeSection={activeSection}
            showSuccess={showSuccess}
            showError={showError}
            showLoading={showLoading}
            showInfo={showInfo}
            clearLoadingNotifications={clearLoadingNotifications}
          />
        )}

        {activeSection === 'payments' && selectedInvoice && selectedClient && selectedProject && (
          <PaymentsManagement
            client={selectedClient}
            project={selectedProject}
            invoice={selectedInvoice}
            selectedPayment={selectedPayment}
            onPaymentSelect={handlePaymentSelect}
            activeSection={activeSection}
            showSuccess={showSuccess}
            showError={showError}
            showLoading={showLoading}
            showInfo={showInfo}
            clearLoadingNotifications={clearLoadingNotifications}
          />
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        title={confirmationModal.title}
        message={confirmationModal.message}
        details={confirmationModal.details}
        confirmText={confirmationModal.verificationData?.canDelete ? "Delete Client" : "Delete Safe Only"}
        cancelText="Cancel"
        onConfirm={confirmationModal.onConfirm}
        onCancel={confirmationModal.onCancel}
        type={confirmationModal.verificationData?.canDelete ? 'danger' : 'verification'}
        showVerification={true}
        verificationData={confirmationModal.verificationData ? {
          canDelete: confirmationModal.verificationData.canDelete,
          reason: confirmationModal.verificationData.reason,
          dependencies: confirmationModal.verificationData.dependencies
        } : undefined}
      />
      
    </div>
  )
}

// ClientRow Component
interface ClientRowProps {
  client: Client
  viewMode: ViewMode
  displayDensity: DisplayDensity
  visibleColumns: Record<string, boolean>
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  actionLoading: string | null
  selectedClient: Client | null
  onClientSelect: (client: Client | null) => void
}

const ClientRow = React.memo(({
  client,
  viewMode,
  displayDensity,
  visibleColumns,
  isSelected,
  onSelect,
  onAction,
  actionLoading,
  selectedClient,
  onClientSelect
}: ClientRowProps) => {
  const isCompact = displayDensity === 'compact'
  const isSpacious = displayDensity === 'spacious'
  const isCurrentlySelected = selectedClient?.id === client.id

  // List view
  if (viewMode === 'list') {
    return (
      <tr
        className={`hover:bg-gray-50 cursor-pointer ${
          isCurrentlySelected ? 'bg-blue-50' : ''
        } ${
          isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
        } ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}
        onClick={() => onClientSelect(client)}
      >
        <td className={`pl-2 whitespace-nowrap ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`} style={{ width: '6px' }}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            onClick={(e) => e.stopPropagation()}
            className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
              displayDensity === 'compact' ? 'h-4 w-4' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'
            }`}
          />
        </td>

        {visibleColumns.companyName && (
          <td className={`pl-1 pr-6 whitespace-nowrap w-1/6 ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <div className="flex items-center">
              {/* Company Avatar */}
              <div className={`flex items-center justify-center mr-1 ${displayDensity === 'compact' ? 'w-8 h-8' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'}`}>
                <ClientAvatar client={{ ...client, id: String(client.id) }} size="sm" />
              </div>

              {/* Company Name */}
              <div className="flex-1 min-w-0">
                <span className={`text-gray-500 truncate font-bold ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
                  {client.companyName}
                </span>
              </div>
            </div>
          </td>
        )}

        {visibleColumns.contactName && (
          <td className={`px-6 whitespace-nowrap w-1/8 min-w-[120px] hidden sm:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.contactName}
            </span>
          </td>
        )}

        {visibleColumns.contactEmail && (
          <td className={`px-6 whitespace-nowrap w-1/4 min-w-[180px] hidden md:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.contactEmail}
            </span>
          </td>
        )}

        {visibleColumns.contactPhone && (
          <td className={`px-6 whitespace-nowrap w-1/8 min-w-[120px] hidden lg:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.contactPhone || 'N/A'}
            </span>
          </td>
        )}

        {visibleColumns.city && (
          <td className={`px-6 whitespace-nowrap w-1/12 min-w-[100px] hidden xl:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.city || 'N/A'}
            </span>
          </td>
        )}

        {visibleColumns.updatedAt && (
          <td className={`px-6 whitespace-nowrap w-1/12 min-w-[100px] hidden lg:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {formatDate(client.updatedAt)}
            </span>
          </td>
        )}

        {visibleColumns.isActive && (
          <td className={`px-6 whitespace-nowrap w-1/12 min-w-[80px] hidden sm:table-cell ${displayDensity === 'compact' ? 'py-1.5' : displayDensity === 'spacious' ? 'py-4.5' : 'py-3'}`}>
            <span className={`text-gray-500 truncate ${displayDensity === 'compact' ? 'text-sm' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.isActive === true ? 'Active' : 'Inactive'}
            </span>
          </td>
        )}

        {visibleColumns.actions && (
          <td className={`px-6 whitespace-nowrap font-medium w-1/12 min-w-[120px] ${displayDensity === 'compact' ? 'py-1.5 text-sm' : displayDensity === 'spacious' ? 'py-4.5 text-base' : 'py-3 text-sm'}`}>
            <div className={`flex items-center justify-start ${displayDensity === 'compact' ? 'space-x-2' : displayDensity === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
                className="text-blue-600 hover:text-blue-900 p-1"
              title="Edit client"
            >
                <PencilIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('toggle-status')
              }}
                className={`p-1 ${
                  client.isActive
                    ? 'text-green-600 hover:text-green-900'
                    : 'text-gray-400 hover:text-gray-600'
                }`}
                title={client.isActive ? 'Deactivate client' : 'Activate client'}
              >
                {client.isActive ? (
                  <EyeSlashIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
                ) : (
                  <EyeIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
                )}
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
                className="text-red-600 hover:text-red-900 p-1"
              title="Delete client"
            >
                <TrashIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            </button>
          </div>
          </td>
        )}
      </tr>
    )
  }

  // Grid view
  if (viewMode === 'grid') {
    // Apply density-based padding
    const getDensityPadding = () => {
      switch (displayDensity) {
        case 'compact': return 'p-3'
        case 'comfortable': return 'p-4'
        case 'spacious': return 'p-5'
        default: return 'p-4'
      }
    }

    return (
      <div
        key={client.id}
        className={`group relative border border-gray-200 rounded-lg cursor-pointer overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md ${
          isSelected 
            ? 'bg-blue-50 border-l-4 border-blue-500' 
            : isCurrentlySelected
            ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300'
            : 'bg-white hover:bg-gray-50/50 hover:border-gray-300'
        } ${getDensityPadding()}`}
        onClick={() => onClientSelect(client)}
        onMouseEnter={(e) => {
          // Show/hide action menu on hover (desktop only)
          if (window.innerWidth > 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
        onMouseLeave={(e) => {
          // Hide action menu when mouse leaves (desktop only)
          if (window.innerWidth > 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '0';
              actionMenu.style.transform = 'translateX(100%)';
              actionMenu.style.pointerEvents = 'none';
            }
          }
        }}
        onTouchStart={(e) => {
          // Show action menu on touch (mobile and small screens)
          if (window.innerWidth <= 1024) {
            e.preventDefault();
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
      >
        {/* Header Section */}
        <div className={`flex items-start justify-between ${displayDensity === 'compact' ? 'mb-1' : displayDensity === 'spacious' ? 'mb-3' : 'mb-2'}`}>
          <div className={`flex items-start flex-1 min-w-0 ${displayDensity === 'compact' ? 'space-x-1' : displayDensity === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
            {/* Checkbox */}
            <div className="flex-shrink-0 pt-1" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            onClick={(e) => e.stopPropagation()}
                className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                  displayDensity === 'compact' 
                    ? 'h-3 w-3' 
                    : displayDensity === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`}
          />
        </div>
            {/* Avatar Container */}
            <div className={`flex-shrink-0 ${displayDensity === 'compact' ? 'w-12 h-12' : displayDensity === 'spacious' ? 'w-16 h-16' : 'w-14 h-14'}`}>
              <ClientAvatar 
                client={{ ...client, id: String(client.id) }} 
                size="sm" 
                className="w-full h-full"
                style={{ width: '100%', height: '100%' }}
              />
          </div>
            
            {/* Title and Description */}
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-gray-900 truncate ${displayDensity === 'compact' ? 'text-sm mb-1' : displayDensity === 'spacious' ? 'text-lg mb-3' : 'text-base mb-2'}`}>
                {client.companyName}
              </h3>
              {client.contactName && (
                <p className={`text-gray-600 line-clamp-2 leading-relaxed ${displayDensity === 'compact' ? 'text-xs' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
                  {client.contactName}
                </p>
              )}
            </div>
            </div>

          {/* Status Badge */}
          <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
            client.isActive
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
              client.isActive ? 'bg-green-400' : 'bg-red-400'
            }`}></div>
            {client.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>

        {/* Divider */}
        <div className={`border-t border-gray-100 ${displayDensity === 'compact' ? 'my-0.5' : displayDensity === 'spacious' ? 'my-2' : 'my-1'}`}></div>

        {/* Info Grid */}
        <div className={`grid grid-cols-2 ${displayDensity === 'compact' ? 'gap-2 mb-0.5' : displayDensity === 'spacious' ? 'gap-6 mb-2' : 'gap-4 mb-1'}`}>
          {/* Contact Email */}
          <div className={`${displayDensity === 'compact' ? 'space-y-0' : displayDensity === 'spacious' ? 'space-y-1' : 'space-y-0.5'}`}>
            <div className={`font-medium text-gray-500 uppercase tracking-wide ${displayDensity === 'compact' ? 'text-xs' : displayDensity === 'spacious' ? 'text-sm' : 'text-xs'}`}>Email</div>
            <div className={`font-medium text-gray-600 truncate ${displayDensity === 'compact' ? 'text-xs' : displayDensity === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {client.contactEmail}
          </div>
          </div>

          {/* Projects Count */}
          <div className={`${displayDensity === 'compact' ? 'space-y-0' : displayDensity === 'spacious' ? 'space-y-1' : 'space-y-0.5'}`}>
            <div className={`font-medium text-gray-500 uppercase tracking-wide ${displayDensity === 'compact' ? 'text-xs' : displayDensity === 'spacious' ? 'text-sm' : 'text-xs'}`}>Projects</div>
            <div className={`flex items-center ${displayDensity === 'compact' ? 'space-x-1' : displayDensity === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
              <span className={`inline-flex items-center bg-blue-100 text-blue-800 font-medium rounded-md ${displayDensity === 'compact' ? 'px-1 py-0 text-xs' : displayDensity === 'spacious' ? 'px-2 py-1 text-base' : 'px-1 py-0 text-sm'}`}>
                {client._count?.projects || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className={`flex items-center justify-between text-gray-500 border-t border-gray-100 ${displayDensity === 'compact' ? 'text-xs pt-0.5' : displayDensity === 'spacious' ? 'text-sm pt-2' : 'text-xs pt-1'}`}>
          <span className={`flex items-center ${displayDensity === 'compact' ? 'space-x-1' : displayDensity === 'spacious' ? 'space-x-2' : 'space-x-1'}`}>
            <span className="font-medium">Invoices:</span>
            <span className={`bg-gray-100 rounded ${displayDensity === 'compact' ? 'px-1 py-0' : displayDensity === 'spacious' ? 'px-2 py-1' : 'px-1 py-0'}`}>{client._count?.invoices || 0}</span>
          </span>
          <div className="flex items-center space-x-2">
            <span className={`flex items-center ${displayDensity === 'compact' ? 'space-x-1' : displayDensity === 'spacious' ? 'space-x-2' : 'space-x-1'}`}>
              <span className="font-medium">Updated:</span>
              <span>{formatDate(client.updatedAt)}</span>
            </span>
            
            {/* Mobile Action Button - Always visible on mobile and small screens */}
            <div className="lg:hidden">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                  if (actionMenu) {
                    const isVisible = actionMenu.style.opacity === '1';
                    actionMenu.style.opacity = isVisible ? '0' : '1';
                    actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                    actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                  }
                }}
                className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                title="Show Actions"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Actions Sidebar - Professional Overlay */}
        <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
          displayDensity === 'compact' 
            ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
            : displayDensity === 'spacious' 
            ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
            : 'top-3 right-3 bottom-3 w-12 space-y-4'
        }`} style={{
          opacity: '0',
          transform: 'translateX(100%)',
          pointerEvents: 'none'
        }}>
          {/* Edit Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
            className={`group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Edit Client"
          >
            <PencilIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            </button>
          
          {/* Toggle Active Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('toggle-status')
              }}
            className={`group/btn relative inline-flex items-center justify-center border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            } ${
              client.isActive
                ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500'
                : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'
            }`}
            title={client.isActive ? 'Deactivate Client' : 'Activate Client'}
          >
            {client.isActive ? (
              <EyeSlashIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            ) : (
              <EyeIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            )}
            </button>
          
          {/* Delete Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
            className={`group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Delete Client"
          >
            <TrashIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            </button>
          </div>
        </div>
    )
  }

  // Card view
  return (
    <motion.div
      className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md ${
        isCurrentlySelected
          ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300'
          : 'hover:bg-gray-50/50 hover:border-gray-300'
      } p-4`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
      onClick={() => onClientSelect(client)}
    >
      {/* Header Section */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          <div className="flex-shrink-0 p-2 bg-blue-50 rounded-lg">
            <ClientAvatar client={{ ...client, id: String(client.id) }} size="sm" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg text-gray-900 truncate mb-2">
              {client.companyName}
            </h3>
            {client.contactName && (
              <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                {client.contactName}
              </p>
            )}
          </div>
        </div>

        <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
          client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {client.isActive ? 'Active' : 'Inactive'}
        </span>
      </div>

      {/* Content */}
      <div className="space-y-2 text-sm text-gray-600">
        {client.contactEmail && (
          <div className="flex items-center">
            <EnvelopeIcon className="h-4 w-4 mr-2" />
            <span>{client.contactEmail}</span>
          </div>
        )}
        {client.contactPhone && (
          <div className="flex items-center">
            <PhoneIcon className="h-4 w-4 mr-2" />
            <span>{client.contactPhone}</span>
          </div>
        )}
        {client.city && (
          <div className="flex items-center">
            <MapPinIcon className="h-4 w-4 mr-2" />
            <span>{client.city}, {client.country}</span>
          </div>
        )}
      </div>

      {/* Checkbox */}
      <div className="mt-4">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onSelect}
          onClick={(e) => e.stopPropagation()}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
    </motion.div>
  )
})
