'use client'

// Imports
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import '@/styles/components/modals.css'
import {
  BuildingOfficeIcon,
  UserIcon,
  MapPinIcon,
  PhotoIcon,
  CheckCircleIcon,
  PencilIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

// Props
interface ClientModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  client?: any
  fields?: any[]
  layout?: any
  showSuccess?: (title: string, message: string) => void
  showError?: (title: string, message: string) => void
}

// Types
interface FormData {
  companyName: string
  website: string
  contactName: string
  contactEmail: string
  contactPhone: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  logoUrl: string
  notes: string
  isActive: boolean
  userId: string
}

interface Position {
  x: number
  y: number
}

interface Size {
  width: number
  height: number
}

interface User {
  value: number
  label: string
}

export function ClientModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  client,
  showSuccess,
  showError
}: ClientModalProps) {
  // State Management - Modal positioning and interaction
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 })
  const [size, setSize] = useState<Size>({ width: 1200, height: 600 })
  const [isReady, setIsReady] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 })
  const [resizeStart, setResizeStart] = useState<Position & Size>({ x: 0, y: 0, width: 0, height: 0 })
  const [windowSize, setWindowSize] = useState<Size>({ width: 0, height: 0 })

  // Refs for DOM manipulation and performance
  const elementRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const actualContentRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // State Management - Form data and validation
  const [formData, setFormData] = useState<FormData>({
    companyName: initialData?.companyName || '',
    website: initialData?.website || '',
    contactName: initialData?.contactName || '',
    contactEmail: initialData?.contactEmail || '',
    contactPhone: initialData?.contactPhone || '',
    address: initialData?.address || '',
    city: initialData?.city || '',
    state: initialData?.state || '',
    zipCode: initialData?.zipCode || '',
    country: initialData?.country || '',
    logoUrl: initialData?.logoUrl || '',
    notes: initialData?.notes || '',
    isActive: initialData?.isActive ?? true,
    userId: '',
  })

  // State Management - UI states
  const [uploadingLogo, setUploadingLogo] = useState(false)
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [loadingUsers, setLoadingUsers] = useState(false)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})

  // Validation - Field validation with business rules
  const validateField = useCallback((fieldName: string, value: string): string => {
    const trimmedValue = value.trim()
    
    switch (fieldName) {
      case 'companyName':
        if (!trimmedValue) return 'Company Name is required'
        if (trimmedValue.length > 200) return 'Company Name must be 200 characters or less'
        return ''
      
      case 'contactName':
        if (!trimmedValue) return 'Contact Name is required'
        if (trimmedValue.length > 100) return 'Contact Name must be 100 characters or less'
        return ''
      
      case 'contactEmail':
        if (!trimmedValue) return 'Contact Email is required'
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmedValue)) {
          return 'Please enter a valid email address'
        }
        if (trimmedValue.length > 100) return 'Email must be 100 characters or less'
        return ''
      
      case 'contactPhone':
        if (trimmedValue && trimmedValue.length > 20) {
          return 'Phone number must be 20 characters or less'
        }
        return ''
      
      case 'website':
        if (trimmedValue && !/^https?:\/\/.+/.test(trimmedValue)) {
          return 'Website must start with http:// or https://'
        }
        if (trimmedValue && trimmedValue.length > 100) {
          return 'Website must be 100 characters or less'
        }
        return ''
      
      case 'address':
        if (trimmedValue && trimmedValue.length > 200) {
          return 'Address must be 200 characters or less'
        }
        return ''
      
      case 'city':
        if (trimmedValue && trimmedValue.length > 100) {
          return 'City must be 100 characters or less'
        }
        return ''
      
      case 'state':
        if (trimmedValue && trimmedValue.length > 50) {
          return 'State must be 50 characters or less'
        }
        return ''
      
      case 'zipCode':
        if (trimmedValue && trimmedValue.length > 20) {
          return 'ZIP Code must be 20 characters or less'
        }
        return ''
      
      case 'country':
        if (trimmedValue && trimmedValue.length > 100) {
          return 'Country must be 100 characters or less'
        }
        return ''
      
      case 'logoUrl':
        if (trimmedValue && trimmedValue.length > 500) {
          return 'Logo URL must be 500 characters or less'
        }
        return ''
      
      case 'userId':
        // User ID is optional, but if provided should be a valid number
        if (trimmedValue && isNaN(Number(trimmedValue))) {
          return 'User ID must be a valid number'
        }
        return ''
      
      case 'notes':
        // Notes have no length limit - free text field
        return ''
      
      case 'isActive':
        // isActive should be a boolean, but we receive it as string from checkbox
        if (value !== 'true' && value !== 'false') {
          return 'Active status must be true or false'
        }
        return ''
      
      default:
        return ''
    }
  }, [])

  // Handlers - Form field interactions
  const handleFieldChange = useCallback((fieldName: string, value: string) => {
    // Handle special cases for different field types
    if (fieldName === 'isActive') {
      setFormData(prev => ({ ...prev, [fieldName]: value === 'true' }))
    } else {
      setFormData(prev => ({ ...prev, [fieldName]: value }))
    }
    
    // Clear error when user starts typing - provides immediate feedback
    if (fieldErrors[fieldName]) {
      setFieldErrors(prev => ({ ...prev, [fieldName]: '' }))
    }
  }, [fieldErrors])

  const handleFieldBlur = useCallback((fieldName: string, value: string) => {
    const error = validateField(fieldName, value)
    setFieldErrors(prev => ({ ...prev, [fieldName]: error }))
  }, [validateField])

  // Effects - Window resize tracking
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })
    }
    
    if (typeof window !== 'undefined') {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Effects - Modal positioning and auto-height calculation
  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      setIsReady(false) // Hide modal while calculating to prevent flash
      
      // Dynamic sizing based on screen size - responsive breakpoints
      const isMobile = windowSize.width < 768
      const isTablet = windowSize.width < 1024
      
      const modalWidth = isMobile 
        ? windowSize.width - 16 
        : isTablet 
          ? Math.min(800, windowSize.width - 32) 
          : Math.min(1200, windowSize.width - 32)
      
      const initialHeight = isMobile ? windowSize.height - 32 : 600
      
      // Calculate center position with viewport bounds
      const viewportWidth = windowSize.width
      const viewportHeight = windowSize.height
      
      const left = Math.max(8, (viewportWidth - modalWidth) / 2)
      const top = Math.max(8, (viewportHeight - initialHeight) / 2)
      
      // Ensure modal stays within viewport bounds
      const finalLeft = Math.min(left, viewportWidth - modalWidth - 8)
      const finalTop = Math.min(top, viewportHeight - initialHeight - 8)
      
      // Set initial size and position
      setSize({ width: modalWidth, height: initialHeight })
      setPosition({ x: finalLeft, y: finalTop })
      
      // Show modal and calculate auto-height after content renders
      setTimeout(() => {
        setIsReady(true)
        
        // Auto-height calculation - measure content and adjust modal height
        setTimeout(() => {
          if (actualContentRef.current) {
            const contentHeight = actualContentRef.current.scrollHeight
            const headerHeight = isMobile ? 50 : 60
            const footerHeight = isMobile ? 50 : 60
            const padding = isMobile ? 8 : 16
            const totalHeight = contentHeight + headerHeight + footerHeight + padding
            const maxHeight = viewportHeight * 0.95
            const newHeight = Math.min(maxHeight, totalHeight)
            
            // Re-center with new height
            const newTop = Math.max(8, (viewportHeight - newHeight) / 2)
            const finalNewTop = Math.min(newTop, viewportHeight - newHeight - 8)
          
            setSize(prev => ({ ...prev, height: newHeight }))
            setPosition(prev => ({ ...prev, y: finalNewTop }))
          }
        }, 50)
      }, 10)
    } else {
      setIsReady(false)
    }
  }, [isOpen, windowSize])

  // Effects - Form data initialization and updates
  useEffect(() => {
    if (isOpen) {
      const clientData = client || initialData
      if (clientData) {
        // Load existing client data for edit mode
        setFormData({
          companyName: clientData.companyName || '',
          website: clientData.website || clientData.companyWebsite || '',
          contactName: clientData.contactName || '',
          contactEmail: clientData.contactEmail || '',
          contactPhone: clientData.contactPhone || '',
          address: clientData.address || '',
          city: clientData.city || '',
          state: clientData.state || '',
          zipCode: clientData.zipCode || '',
          country: clientData.country || '',
          logoUrl: clientData.logoUrl || '',
          notes: clientData.notes || '',
          isActive: clientData.isActive ?? true,
          userId: clientData.userId ? String(clientData.userId) : '',
        })
      } else {
        // Reset form for create mode
        setFormData({
          companyName: '',
          website: '',
          contactName: '',
          contactEmail: '',
          contactPhone: '',
          address: '',
          city: '',
          state: '',
          zipCode: '',
          country: '',
          logoUrl: '',
          notes: '',
          isActive: true,
          userId: '',
        })
      }
    }
  }, [isOpen, client, initialData])

  // Handlers - API calls and data fetching
  const fetchAvailableUsers = useCallback(async () => {
    setLoadingUsers(true)
    try {
      const clientData = client || initialData
      const excludeClientId = clientData?.id ? `?excludeClientId=${clientData.id}` : ''
      const response = await fetch(`/api/admin/users/available${excludeClientId}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAvailableUsers(data.data)
        } else {
          console.error('API returned success: false', data)
          setAvailableUsers([])
        }
      } else {
        const errorData = await response.json()
        console.error('API error response:', errorData)
        setAvailableUsers([])
      }
    } catch (error) {
      console.error('Error fetching available users:', error)
      setAvailableUsers([])
    } finally {
      setLoadingUsers(false)
    }
  }, [client, initialData])

  // Handlers - Modal interaction (dragging and resizing)
  const handleBorderResizeMouseDown = useCallback((e: React.MouseEvent, direction: string) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    })
    // Store resize direction for mouse move handler
    e.currentTarget.setAttribute('data-resize-direction', direction)
  }, [size.width, size.height])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    })
  }, [position.x, position.y])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && elementRef.current) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y
      
      // Keep modal within viewport bounds - prevent dragging outside screen
      const maxX = window.innerWidth - size.width
      const maxY = window.innerHeight - size.height
      
      const constrainedX = Math.max(0, Math.min(maxX, newX))
      const constrainedY = Math.max(0, Math.min(maxY, newY))
      
      // Immediate DOM update - no state updates during drag for maximum performance
      elementRef.current.style.left = `${constrainedX}px`
      elementRef.current.style.top = `${constrainedY}px`
      elementRef.current.style.transform = 'none'
      elementRef.current.style.transition = 'none'
    } else if (isResizing && elementRef.current) {
      const deltaX = e.clientX - resizeStart.x
      const deltaY = e.clientY - resizeStart.y
      
      // Get resize direction from the element that initiated the resize
      const resizeElement = document.querySelector('[data-resize-direction]')
      const direction = resizeElement?.getAttribute('data-resize-direction') || 'se'
      
      let newWidth = resizeStart.width
      let newHeight = resizeStart.height
      
      // Handle different resize directions - cardinal directions
      if (direction.includes('e')) { // East (right)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width + deltaX))
      }
      if (direction.includes('w')) { // West (left)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width - deltaX))
      }
      if (direction.includes('s')) { // South (bottom)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height + deltaY))
      }
      if (direction.includes('n')) { // North (top)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height - deltaY))
      }
      
      // Immediate DOM update - no state updates during resize for maximum performance
      elementRef.current.style.width = `${newWidth}px`
      elementRef.current.style.height = `${newHeight}px`
      elementRef.current.style.transition = 'none'
    }
  }, [isDragging, isResizing, dragStart, resizeStart, size.width, size.height])

  const handleMouseUp = useCallback(() => {
    // Sync state with final DOM position for consistency after drag/resize
    if (elementRef.current && (isDragging || isResizing)) {
      const rect = elementRef.current.getBoundingClientRect()
      setPosition({ x: rect.left, y: rect.top })
      setSize({ width: rect.width, height: rect.height })
    }
    
    // Restore transitions when interaction ends - smooth animations resume
    if (elementRef.current) {
      elementRef.current.style.transition = 'box-shadow 0.2s ease'
    }
    
    // Clean up resize direction attribute - prevent memory leaks
    const resizeElement = document.querySelector('[data-resize-direction]')
    if (resizeElement) {
      resizeElement.removeAttribute('data-resize-direction')
    }
    
    setIsDragging(false)
    setIsResizing(false)
  }, [isDragging, isResizing])

  // Effects - Event listeners for modal interactions
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp, { passive: true })
      document.addEventListener('mouseleave', handleMouseUp, { passive: true })
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('mouseleave', handleMouseUp)
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // Effects - Click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (elementRef.current && !elementRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  // Effects - Window resize handling - keep modal in viewport
  useEffect(() => {
    const handleWindowResize = () => {
      if (elementRef.current && isOpen) {
        const rect = elementRef.current.getBoundingClientRect()
        const maxX = windowSize.width - size.width
        const maxY = windowSize.height - size.height
        
        if (rect.right > windowSize.width || rect.bottom > windowSize.height) {
          const newX = Math.max(8, Math.min(maxX, position.x))
          const newY = Math.max(8, Math.min(maxY, position.y))
          setPosition({ x: newX, y: newY })
        }
      }
    }

    if (isOpen) {
      handleWindowResize()
    }
  }, [windowSize, isOpen, position, size])

  // Effects - Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Effects - Fetch users when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAvailableUsers()
    }
  }, [isOpen, fetchAvailableUsers])

  // Handlers - File upload functionality
  const handleFileUpload = useCallback(async (file: File) => {
    setUploadingLogo(true)
    try {
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      const response = await fetch('/api/upload/client-logo', {
        method: 'POST',
        body: uploadFormData
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.status} - ${errorText}`)
      }

      const result = await response.json()

      if (result.success) {
        setFormData(prev => ({ ...prev, logoUrl: result.data.url }))
        if (showSuccess) {
          showSuccess('Logo Uploaded', 'Logo uploaded successfully!')
        } else {
          alert(`Logo uploaded successfully! File saved to: ${result.data.url}`)
        }
      } else {
        throw new Error(result.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      if (showError) {
        showError('Upload Failed', `Failed to upload logo: ${error instanceof Error ? error.message : 'Unknown error'}`)
      } else {
        alert(`Failed to upload logo: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    } finally {
      setUploadingLogo(false)
    }
  }, [showSuccess, showError])

  // Handlers - Form submission with validation
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate all fields and collect errors
    const newFieldErrors: Record<string, string> = {}
    
    // Validate all fields using the validation function
    Object.keys(formData).forEach(fieldName => {
      if (typeof formData[fieldName as keyof typeof formData] === 'string') {
        const error = validateField(fieldName, formData[fieldName as keyof typeof formData] as string)
        if (error) {
          newFieldErrors[fieldName] = error
        }
      }
    })
    
    // Update field errors state
    setFieldErrors(newFieldErrors)
    
    // Check if there are any errors - prevent submission
    if (Object.keys(newFieldErrors).length > 0) {
      // Focus on first error field for better UX
      const firstErrorField = Object.keys(newFieldErrors)[0]
      const errorElement = document.querySelector(`[name="${firstErrorField}"]`) as HTMLInputElement
      if (errorElement) {
        errorElement.focus()
      }
      return
    }
    
    try {
      // Transform form data to match API expectations - clean and trim values
      const submitData = {
        companyName: formData.companyName.trim(),
        contactName: formData.contactName.trim(),
        contactEmail: formData.contactEmail.trim(),
        contactPhone: formData.contactPhone?.trim() || '',
        website: formData.website?.trim() || '',
        address: formData.address?.trim() || '',
        city: formData.city?.trim() || '',
        state: formData.state?.trim() || '',
        zipCode: formData.zipCode?.trim() || '',
        country: formData.country?.trim() || '',
        logoUrl: formData.logoUrl?.trim() || '',
        notes: formData.notes?.trim() || '',
        isActive: formData.isActive,
        ...(formData.userId && { userId: formData.userId }), // Only include if provided
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      alert('Failed to save client. Please try again.')
    }
  }, [formData, validateField, onSubmit, onClose])

  // ========================================
  // RESPONSIVE LAYOUT HELPER
  // ========================================
  const getResponsiveBreakpoints = useMemo(() => {
    // Use modal width for responsive breakpoints when modal is resized
    // Fall back to window width for initial sizing
    const referenceWidth = size.width || windowSize.width
    const isMobile = referenceWidth < 600  // Adjusted for modal context
    const isTablet = referenceWidth < 900  // Adjusted for modal context
    
    return { isMobile, isTablet, referenceWidth }
  }, [size.width, windowSize.width])

  // ========================================
  // FORM SECTIONS - RENDER FUNCTIONS
  // ========================================
  const renderCompanySection = useCallback(() => {
    const { isMobile, isTablet } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
        <h4 className="modal-form-section-motion-title">
          <BuildingOfficeIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
          Company Information
        </h4>
        <div className="modal-form-grid" style={{
          gridTemplateColumns: isMobile ? '1fr' : isTablet ? '1fr' : '1fr 1fr',
          gap: isMobile ? '8px' : '12px'
        }}>
        <div className="modal-form-field">
          <label className="modal-form-label">
            Company Name <span className="modal-required">*</span>
          </label>
          <input
            type="text"
            name="companyName"
            required
            value={formData.companyName}
            onChange={(e) => handleFieldChange('companyName', e.target.value)}
            onBlur={(e) => handleFieldBlur('companyName', e.target.value)}
            placeholder="Enter company name"
            className={`modal-form-input ${fieldErrors.companyName ? 'modal-form-input-error' : ''}`}
            minLength={1}
            maxLength={200}
          />
          {fieldErrors.companyName && (
            <div className="modal-form-error">{fieldErrors.companyName}</div>
          )}
        </div>
        <div className="modal-form-field">
          <label className="modal-form-label">Website</label>
          <input
            type="url"
            name="website"
            value={formData.website}
            onChange={(e) => handleFieldChange('website', e.target.value)}
            onBlur={(e) => handleFieldBlur('website', e.target.value)}
            placeholder="https://example.com"
            className={`modal-form-input ${fieldErrors.website ? 'modal-form-input-error' : ''}`}
          />
          {fieldErrors.website && (
            <div className="modal-form-error">{fieldErrors.website}</div>
          )}
        </div>
      </div>
    </div>
    )
  }, [getResponsiveBreakpoints, formData.companyName, formData.website, fieldErrors.companyName, fieldErrors.website, handleFieldChange, handleFieldBlur])

  const renderContactSection = useCallback(() => {
    const { isMobile, isTablet } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
        <h4 className="modal-form-section-motion-title">
          <UserIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
          Contact Information
        </h4>
        <div className="modal-form-grid" style={{
          gridTemplateColumns: isMobile ? '1fr' : isTablet ? '1fr' : '1fr 1fr',
          gap: isMobile ? '8px' : '12px'
        }}>
        <div className="modal-form-field">
          <label className="modal-form-label">
            Contact Name <span className="modal-required">*</span>
          </label>
          <input
            type="text"
            name="contactName"
            required
            value={formData.contactName}
            onChange={(e) => handleFieldChange('contactName', e.target.value)}
            onBlur={(e) => handleFieldBlur('contactName', e.target.value)}
            placeholder="Contact person"
            className={`modal-form-input ${fieldErrors.contactName ? 'modal-form-input-error' : ''}`}
            minLength={1}
            maxLength={100}
          />
          {fieldErrors.contactName && (
            <div className="modal-form-error">{fieldErrors.contactName}</div>
          )}
        </div>
        <div className="modal-form-field">
          <label className="modal-form-label">
            Email <span className="modal-required">*</span>
          </label>
          <input
            type="email"
            name="contactEmail"
            required
            value={formData.contactEmail}
            onChange={(e) => handleFieldChange('contactEmail', e.target.value)}
            onBlur={(e) => handleFieldBlur('contactEmail', e.target.value)}
            placeholder="<EMAIL>"
            className={`modal-form-input ${fieldErrors.contactEmail ? 'modal-form-input-error' : ''}`}
            minLength={1}
            maxLength={100}
          />
          {fieldErrors.contactEmail && (
            <div className="modal-form-error">{fieldErrors.contactEmail}</div>
          )}
        </div>
        <div className="modal-form-field">
          <label className="modal-form-label">Phone</label>
          <input
            type="tel"
            name="contactPhone"
            value={formData.contactPhone}
            onChange={(e) => handleFieldChange('contactPhone', e.target.value)}
            onBlur={(e) => handleFieldBlur('contactPhone', e.target.value)}
            placeholder="+****************"
            className={`modal-form-input ${fieldErrors.contactPhone ? 'modal-form-input-error' : ''}`}
          />
          {fieldErrors.contactPhone && (
            <div className="modal-form-error">{fieldErrors.contactPhone}</div>
          )}
        </div>
        <div className="modal-form-field">
          <label className="modal-form-label">Linked User Account</label>
          <select
            name="userId"
            value={formData.userId}
            onChange={(e) => handleFieldChange('userId', e.target.value)}
            onBlur={(e) => handleFieldBlur('userId', e.target.value)}
            className={`modal-form-select ${fieldErrors.userId ? 'modal-form-input-error' : ''}`}
            disabled={loadingUsers}
          >
            <option value="">Select a user account to link (optional)</option>
            {availableUsers.map(user => (
              <option key={user.value} value={user.value}>
                {user.label}
              </option>
            ))}
          </select>
          {fieldErrors.userId && (
            <div className="modal-form-error">{fieldErrors.userId}</div>
          )}
          {loadingUsers && (
            <p className="modal-form-error">Loading available users...</p>
          )}
          {availableUsers.length === 0 && !loadingUsers && (
            <p className="text-xs text-amber-600 mt-1">
              No CLIENT role users available to link. Create CLIENT role users first.
            </p>
          )}
        </div>
      </div>
    </div>
    )
  }, [getResponsiveBreakpoints, formData.contactName, formData.contactEmail, formData.contactPhone, formData.userId, fieldErrors.contactName, fieldErrors.contactEmail, fieldErrors.contactPhone, fieldErrors.userId, handleFieldChange, handleFieldBlur, loadingUsers, availableUsers])

  const renderAddressSection = useCallback(() => {
    const { isMobile, isTablet } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
        <h4 className="modal-form-section-motion-title">
          <MapPinIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
          Address Information
        </h4>
        <div className="modal-form-section">
          <div className="modal-form-field">
            <label className="modal-form-label">Address</label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={(e) => handleFieldChange('address', e.target.value)}
              onBlur={(e) => handleFieldBlur('address', e.target.value)}
              placeholder="Street address"
              className={`modal-form-input ${fieldErrors.address ? 'modal-form-input-error' : ''}`}
            />
            {fieldErrors.address && (
              <div className="modal-form-error">{fieldErrors.address}</div>
            )}
          </div>
          <div className="grid" style={{
            gridTemplateColumns: isMobile ? '1fr' : isTablet ? '1fr 1fr' : '1fr 1fr 1fr 1fr',
            gap: isMobile ? '8px' : '12px'
          }}>
          <div className="modal-form-field">
            <label className="modal-form-label">City</label>
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={(e) => handleFieldChange('city', e.target.value)}
              onBlur={(e) => handleFieldBlur('city', e.target.value)}
              placeholder="City"
              className={`modal-form-input ${fieldErrors.city ? 'modal-form-input-error' : ''}`}
            />
            {fieldErrors.city && (
              <div className="modal-form-error">{fieldErrors.city}</div>
            )}
          </div>
          <div className="modal-form-field">
            <label className="modal-form-label">State</label>
            <input
              type="text"
              name="state"
              value={formData.state}
              onChange={(e) => handleFieldChange('state', e.target.value)}
              onBlur={(e) => handleFieldBlur('state', e.target.value)}
              placeholder="State"
              className={`modal-form-input ${fieldErrors.state ? 'modal-form-input-error' : ''}`}
            />
            {fieldErrors.state && (
              <div className="modal-form-error">{fieldErrors.state}</div>
            )}
          </div>
          <div className="modal-form-field">
            <label className="modal-form-label">ZIP Code</label>
            <input
              type="text"
              name="zipCode"
              value={formData.zipCode}
              onChange={(e) => handleFieldChange('zipCode', e.target.value)}
              onBlur={(e) => handleFieldBlur('zipCode', e.target.value)}
              placeholder="ZIP"
              className={`modal-form-input ${fieldErrors.zipCode ? 'modal-form-input-error' : ''}`}
            />
            {fieldErrors.zipCode && (
              <div className="modal-form-error">{fieldErrors.zipCode}</div>
            )}
          </div>
          <div className="modal-form-field">
            <label className="modal-form-label">Country</label>
            <input
              type="text"
              name="country"
              value={formData.country}
              onChange={(e) => handleFieldChange('country', e.target.value)}
              onBlur={(e) => handleFieldBlur('country', e.target.value)}
              placeholder="Country"
              className={`modal-form-input ${fieldErrors.country ? 'modal-form-input-error' : ''}`}
            />
            {fieldErrors.country && (
              <div className="modal-form-error">{fieldErrors.country}</div>
            )}
          </div>
        </div>
      </div>
    </div>
    )
  }, [getResponsiveBreakpoints, formData.address, formData.city, formData.state, formData.zipCode, formData.country, fieldErrors.address, fieldErrors.city, fieldErrors.state, fieldErrors.zipCode, fieldErrors.country, handleFieldChange, handleFieldBlur])

  const renderLogoSection = useCallback(() => {
    const { isMobile } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
      <h4 className="modal-form-section-motion-title">
        <PhotoIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
        Company Logo
      </h4>
      <div className="space-y-3">
        {/* Logo Preview - Interactive upload area */}
        <div className="flex justify-center" style={{ padding: '8px' }}>
          {formData.logoUrl ? (
            <div 
              className="relative w-32 h-32 bg-white border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors" 
              onClick={() => fileInputRef.current?.click()}
              title="Click to upload new logo"
            >
              <img
                src={formData.logoUrl}
                alt="Company logo"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  display: 'block',
                  borderRadius: '6px',
                  margin: 0,
                  padding: 0,
                  border: 'none',
                  outline: 'none'
                }}
              />
              {/* Remove logo button - positioned in top right corner */}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation() // Prevent triggering the upload dialog
                  setFormData(prev => ({ ...prev, logoUrl: '' }))
                }}
                style={{
                  position: 'absolute',
                  top: '2px',
                  right: '2px',
                  width: '24px',
                  height: '24px',
                  backgroundColor: 'transparent',
                  color: '#dc2626',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10,
                  opacity: 0.8,
                  transition: 'opacity 0.2s ease',
                  margin: 0,
                  padding: 0
                }}
                title="Remove logo"
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '1'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '0.8'
                }}
              >
                ×
              </button>
            </div>
          ) : (
            <div 
              className="w-32 h-32 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 hover:bg-gray-50 transition-colors" 
              onClick={() => fileInputRef.current?.click()}
              title="Click to upload logo"
            >
              <PhotoIcon className="h-12 w-12 text-gray-400" />
            </div>
          )}
        </div>

        {/* Hidden file input for upload functionality */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/svg+xml,image/png,image/jpeg,image/jpg,image/webp"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) {
              handleFileUpload(file)
            }
          }}
          className="hidden"
        />

        {/* Logo URL Field with upload button */}
        <div className="modal-form-field">
          <label className="modal-form-label">Logo URL {formData.logoUrl ? '' : '(Optional)'}</label>
          <div className="flex" style={{ gap: '4px' }}>
            <input
              type="url"
              name="logoUrl"
              value={formData.logoUrl}
              onChange={(e) => handleFieldChange('logoUrl', e.target.value)}
              onBlur={(e) => handleFieldBlur('logoUrl', e.target.value)}
              placeholder="https://example.com/logo.png"
              className={`modal-form-input ${fieldErrors.logoUrl ? 'modal-form-input-error' : ''}`}
              style={{ borderRadius: '6px' }}
              readOnly={false}
            />
            {fieldErrors.logoUrl && (
              <div className="modal-form-error">{fieldErrors.logoUrl}</div>
            )}
            {/* Upload button next to URL field */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploadingLogo}
              style={{
                padding: '6px',
                backgroundColor: 'transparent',
                color: '#6b7280',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: uploadingLogo ? 'not-allowed' : 'pointer',
                fontSize: '12px',
                fontWeight: '500',
                opacity: uploadingLogo ? 0.6 : 1,
                transition: 'all 0.2s ease',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: '40px',
                height: '40px',
                flexShrink: 0,
              }}
              title="Upload logo file"
              onMouseEnter={(e) => {
                if (!uploadingLogo) {
                  e.currentTarget.style.backgroundColor = '#f9fafb'
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              <PhotoIcon style={{ width: '22px', height: '22px' }} />
            </button>
          </div>
        </div>
      </div>
    </div>
    )
  }, [getResponsiveBreakpoints, formData.logoUrl, fieldErrors.logoUrl, handleFieldChange, handleFieldBlur, handleFileUpload, uploadingLogo])

  const renderStatusSection = useCallback(() => {
    const { isMobile } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
      <h4 className="modal-form-section-motion-title">
        <CheckCircleIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
        Status
      </h4>
      <div className="flex items-center">
        <input
          type="checkbox"
          id="isActive"
          name="isActive"
          checked={formData.isActive}
          onChange={(e) => handleFieldChange('isActive', e.target.checked.toString())}
          onBlur={(e) => handleFieldBlur('isActive', e.target.checked.toString())}
          className={`modal-form-checkbox ${fieldErrors.isActive ? 'modal-form-input-error' : ''}`}
        />
        <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
          Client is active
        </label>
      </div>
      {fieldErrors.isActive && (
        <div className="modal-form-error">{fieldErrors.isActive}</div>
      )}
    </div>
    )
  }, [getResponsiveBreakpoints, formData.isActive, fieldErrors.isActive, handleFieldChange, handleFieldBlur])

  const renderNotesSection = useCallback(() => {
    const { isMobile } = getResponsiveBreakpoints
    
    return (
      <div className="modal-form-section-motion" style={{
        backgroundColor: 'white',
        borderRadius: isMobile ? '6px' : '8px',
        padding: isMobile ? '12px' : '16px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
      }}>
      <h4 className="modal-form-section-motion-title">
        <PencilIcon className="h-4 w-4" style={{ color: '#3b82f6' }} />
        Notes
      </h4>
      <div className="modal-form-field">
        <label className="modal-form-label">Additional Notes</label>
        <textarea
          rows={4}
          name="notes"
          value={formData.notes}
          onChange={(e) => handleFieldChange('notes', e.target.value)}
          onBlur={(e) => handleFieldBlur('notes', e.target.value)}
          placeholder="Additional notes about this client..."
          className={`modal-form-textarea ${fieldErrors.notes ? 'modal-form-input-error' : ''}`}
        />
        {fieldErrors.notes && (
          <div className="modal-form-error">{fieldErrors.notes}</div>
        )}
      </div>
    </div>
    )
  }, [getResponsiveBreakpoints, formData.notes, fieldErrors.notes, handleFieldChange, handleFieldBlur])

  // ========================================
  // RENDERING - Early return for performance
  // ========================================
  if (!isOpen || !isReady) return null

  return (
    <>
      {/* CSS Animation Keyframes - Inline for performance */}
      <style jsx>{`
        @keyframes modalAppear {
          0% {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
      
      {/* Modal Backdrop/Overlay - Click outside to close */}
      <div
        className="modal-overlay"
        onClick={onClose}
      />
      
      {/* Modal Window - Draggable and resizable */}
        <div
          ref={elementRef}
          className={`modal-container ${isDragging ? 'dragging' : ''} ${isResizing ? 'resizing' : ''}`}
          style={{
            position: 'fixed',
            zIndex: 99999,
            backgroundColor: 'white',
            borderRadius: windowSize.width < 768 ? '8px' : '12px',
            boxShadow: isDragging ? '0 30px 60px -12px rgba(0, 0, 0, 0.5)' : 
                       isResizing ? '0 25px 50px -12px rgba(0, 0, 0, 0.4)' : 
                       '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            overflow: 'hidden',
            cursor: isResizing ? 'nw-resize' : 'default',
            padding: 0,
            margin: 0,
            width: `${size.width}px`,
            height: `${size.height}px`,
            left: `${position.x}px`,
            top: `${position.y}px`,
            isolation: 'isolate',
            contain: 'layout style paint',
            resize: 'none',
            transition: isDragging || isResizing ? 'none' : 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
            animation: 'modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
          }}
        >
        {/* Modal Header - Draggable with title and close button */}
        <div
          className="modal-header-motion blue-gradient"
          style={{
            padding: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '12px 16px' : '16px 24px'
            })(),
            display: 'flex',
            alignItems: 'center',
            gap: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '8px' : '12px'
            })(),
            marginBottom: '-8px',
            cursor: isDragging ? 'grabbing' : 'move',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          }}
          onMouseDown={handleMouseDown}
        >
          {/* Icon Container */}
          <div className="modal-header-motion-icon">
            <BuildingOfficeIcon 
              className="modal-header-motion-icon-svg"
              style={{
                width: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '20px' : '24px'
                })(),
                height: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '20px' : '24px'
                })(),
              }}
            />
          </div>

          {/* Text Container */}
          <div className="modal-header-motion-text">
            <h3 className="modal-header-motion-title" style={{
              fontSize: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '16px' : '20px'
              })(),
            }}>
              {title}
            </h3>
            <p className="modal-header-motion-subtitle" style={{
              fontSize: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '12px' : '14px'
              })(),
            }}>
              {(client || initialData) ? 'Edit Client Information' : 'Create New Client'}
            </p>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="modal-header-motion-close"
          >
            <XMarkIcon 
              className="modal-header-motion-close-icon"
            style={{
              width: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '16px' : '20px'
              })(),
              height: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '16px' : '20px'
              })(),
            }}
            />
          </button>
        </div>

        {/* Modal Content - Scrollable form area */}
        <div
          ref={contentRef}
          className="modal-content"
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: 'calc(100% - 60px)',
            paddingBottom: '0px',
            opacity: 1,
            transform: 'none',
            backgroundColor: 'transparent',
            flex: 1,
          }}
        >
          <div
            className="modal-content-body"
            style={{
              flex: 1,
              padding: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '8px' : '12px'
              })(),
              cursor: 'default',
              overflowY: 'auto',
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
            }}
          >
            <div ref={actualContentRef} key="content-wrapper" style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              gap: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '8px' : '12px'
              })()
            }}>
              <form onSubmit={handleSubmit} className="modal-form">
                <div className="modal-form-grid" style={{
                  gridTemplateColumns: (() => {
                    const { isMobile, isTablet } = getResponsiveBreakpoints
                    return isMobile ? '1fr' : isTablet ? '1fr' : '2fr 1fr'
                  })(),
                  gap: (() => {
                    const { isMobile } = getResponsiveBreakpoints
                    return isMobile ? '8px' : '12px'
                  })()
                }}>
                  {/* Left Column - Main Info */}
                  <div className="space-y-2">
                    {renderCompanySection()}
                    {renderContactSection()}
                    {renderAddressSection()}
                  </div>

                  {/* Right Column - Logo & Status */}
                  <div className="space-y-2">
                    {renderLogoSection()}
                    {renderStatusSection()}
                    {renderNotesSection()}
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* Modal Footer - Status and action buttons */}
        <div
          className="modal-footer centered"
          style={{
            backgroundColor: 'white',
            padding: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '16px' : '24px'
            })(),
            borderTop: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            minHeight: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '50px' : '60px'
            })(),
            opacity: 1,
            transform: 'none',
            borderBottomLeftRadius: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '8px' : '12px'
            })(),
            borderBottomRightRadius: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '8px' : '12px'
            })(),
          }}
        >
          {/* Status Message - Left aligned with last updated info */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#64748b',
            fontSize: '14px',
            position: 'absolute',
            left: (() => {
              const { isMobile } = getResponsiveBreakpoints
              return isMobile ? '16px' : '24px'
            })(),
            top: '50%',
            transform: 'translateY(-50%)',
          }}>
            <CheckCircleIcon style={{ width: '16px', height: '16px' }} />
            <span>{(client || initialData) ? 'Last updated: ' + new Date((client || initialData).updatedAt || Date.now()).toLocaleDateString() : 'Creating new client'}</span>
          </div>
          
          {/* Action Buttons - Centered with responsive sizing */}
          <div style={{
              display: 'flex',
              gap: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '8px' : '12px'
              })(),
              position: 'absolute',
              left: '50%',
              top: (() => {
                const { isMobile } = getResponsiveBreakpoints
                return isMobile ? '8px' : '12px'
              })(),
              transform: 'translateX(-50%)',
            }}>
            <button
              type="button"
              onClick={onClose}
              disabled={uploadingLogo}
              style={{
                padding: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '8px 16px' : '12px 24px'
                })(),
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: uploadingLogo ? 'not-allowed' : 'pointer',
                fontSize: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '12px' : '14px'
                })(),
                fontWeight: '500',
                opacity: uploadingLogo ? 0.6 : 1,
                transition: 'all 0.2s ease',
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={uploadingLogo}
              style={{
                padding: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '8px 16px' : '12px 24px'
                })(),
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: uploadingLogo ? 'not-allowed' : 'pointer',
                fontSize: (() => {
                  const { isMobile } = getResponsiveBreakpoints
                  return isMobile ? '12px' : '14px'
                })(),
                fontWeight: '500',
                opacity: uploadingLogo ? 0.6 : 1,
                transition: 'all 0.2s ease',
              }}
            >
              {uploadingLogo ? 'Saving...' : ((client || initialData) ? 'Update Client' : 'Create Client')}
            </button>
          </div>
        </div>

        {/* Border Resize Handles - Hidden on mobile for better UX */}
        {!getResponsiveBreakpoints.isMobile && (
          <>
            {/* Top border - North resize */}
            <div
              className="modal-resize-handle modal-resize-handle-top"
              onMouseDown={(e) => handleBorderResizeMouseDown(e, 'n')}
            />
            
            {/* Right border - East resize */}
            <div
              className="modal-resize-handle modal-resize-handle-right"
              onMouseDown={(e) => handleBorderResizeMouseDown(e, 'e')}
            />
            
            {/* Bottom border - South resize */}
            <div
              className="modal-resize-handle modal-resize-handle-bottom"
              onMouseDown={(e) => handleBorderResizeMouseDown(e, 's')}
            />
            
            {/* Left border - West resize */}
            <div
              className="modal-resize-handle modal-resize-handle-left"
              onMouseDown={(e) => handleBorderResizeMouseDown(e, 'w')}
            />
          </>
        )}
      </div>
    </>
  )
}
